{"name": "lms-vue-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.5.1", "pinia": "^3.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/forms": "^0.5.10", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^3.0.3"}}