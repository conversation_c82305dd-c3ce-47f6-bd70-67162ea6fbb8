import { createRouter, createWebHistory } from 'vue-router'

// Auth views
const LoginView = () => import('@/views/auth/LoginView.vue')

// Admin views
const AdminDashboard = () => import('@/views/admin/AdminDashboard.vue')
const UserManagement = () => import('@/views/admin/UserManagement.vue')

// Teacher views
const TeacherDashboard = () => import('@/views/teacher/TeacherDashboard.vue')
const ExamCreation = () => import('@/views/teacher/ExamCreation.vue')
const ExamManagement = () => import('@/views/teacher/ExamManagement.vue')
const GradingView = () => import('@/views/teacher/GradingView.vue')

// Student views
const StudentDashboard = () => import('@/views/student/StudentDashboard.vue')
const ExamTaking = () => import('@/views/student/ExamTaking.vue')
const ExamResults = () => import('@/views/student/ExamResults.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: LoginView
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    // Admin routes
    {
      path: '/admin',
      name: 'admin',
      component: AdminDashboard,
      meta: { requiresAuth: true, role: 'admin' }
    },
    {
      path: '/admin/users',
      name: 'admin-users',
      component: UserManagement,
      meta: { requiresAuth: true, role: 'admin' }
    },
    // Teacher routes
    {
      path: '/teacher',
      name: 'teacher',
      component: TeacherDashboard,
      meta: { requiresAuth: true, role: 'teacher' }
    },
    {
      path: '/teacher/exams/create',
      name: 'exam-create',
      component: ExamCreation,
      meta: { requiresAuth: true, role: 'teacher' }
    },
    {
      path: '/teacher/exams',
      name: 'exam-management',
      component: ExamManagement,
      meta: { requiresAuth: true, role: 'teacher' }
    },
    {
      path: '/teacher/grading/:examId',
      name: 'grading',
      component: GradingView,
      meta: { requiresAuth: true, role: 'teacher' }
    },
    // Student routes
    {
      path: '/student',
      name: 'student',
      component: StudentDashboard,
      meta: { requiresAuth: true, role: 'student' }
    },
    {
      path: '/student/exam/:examId',
      name: 'exam-taking',
      component: ExamTaking,
      meta: { requiresAuth: true, role: 'student' }
    },
    {
      path: '/student/results',
      name: 'exam-results',
      component: ExamResults,
      meta: { requiresAuth: true, role: 'student' }
    }
  ]
})

// Navigation guards
router.beforeEach((to, _from, next) => {
  // Check if user is authenticated by checking localStorage
  const savedUser = localStorage.getItem('user')
  let user = null

  if (savedUser) {
    try {
      user = JSON.parse(savedUser)
    } catch (error) {
      localStorage.removeItem('user')
    }
  }

  const isAuthenticated = !!user

  // Handle root path redirect based on authentication
  if (to.path === '/') {
    if (isAuthenticated) {
      const role = user?.role
      if (role === 'admin') {
        next('/admin')
      } else if (role === 'teacher') {
        next('/teacher')
      } else if (role === 'student') {
        next('/student')
      } else {
        next('/login')
      }
    } else {
      next('/login')
    }
    return
  }

  // Allow access to login page
  if (to.path === '/login') {
    if (isAuthenticated) {
      // Redirect authenticated users to their dashboard
      const role = user?.role
      if (role === 'admin') {
        next('/admin')
      } else if (role === 'teacher') {
        next('/teacher')
      } else if (role === 'student') {
        next('/student')
      } else {
        next()
      }
    } else {
      next()
    }
    return
  }

  // Check authentication for protected routes
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
    return
  }

  // Check role-based access
  if (to.meta.role && user?.role !== to.meta.role) {
    next('/login')
    return
  }

  // Allow access
  next()
})

export default router