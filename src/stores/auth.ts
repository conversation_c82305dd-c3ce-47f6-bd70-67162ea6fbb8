import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const isLoading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userRole = computed(() => user.value?.role)

  // Mock users for demonstration
  const mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: '<PERSON>',
      firstName: '<PERSON>',
      lastName: 'Teacher',
      role: 'teacher',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        teacherId: 'T001',
        department: 'Mathematics',
        subjects: ['Math', 'Statistics']
      }
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: '<PERSON>',
      firstName: '<PERSON>',
      lastName: 'Student',
      role: 'student',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        studentId: 'S001',
        grade: '10th Grade'
      }
    }
  ]

  // Actions
  const login = async (email: string, password: string): Promise<boolean> => {
    isLoading.value = true

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Find user by email (in real app, this would be server-side)
      const foundUser = mockUsers.find(u => u.email === email)

      if (foundUser && password === 'password') { // Simple password check for demo
        user.value = foundUser
        localStorage.setItem('user', JSON.stringify(foundUser))
        return true
      }

      return false
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    user.value = null
    localStorage.removeItem('user')
  }

  const initializeAuth = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('user')
      }
    }
  }

  const updateUser = (updatedUser: User) => {
    user.value = updatedUser
    localStorage.setItem('user', JSON.stringify(updatedUser))
  }

  return {
    // State
    user,
    isLoading,

    // Getters
    isAuthenticated,
    userRole,

    // Actions
    login,
    logout,
    initializeAuth,
    updateUser
  }
})