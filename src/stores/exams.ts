import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Exam, Question, QuestionBank, ExamSubmission, Answer, ApiResponse } from '@/types'

export const useExamStore = defineStore('exams', () => {
  // State
  const exams = ref<Exam[]>([])
  const currentExam = ref<Exam | null>(null)
  const submissions = ref<ExamSubmission[]>([])
  const questionBanks = ref<QuestionBank[]>([])
  const isLoading = ref(false)
  const searchQuery = ref('')
  const filters = ref({
    category: undefined as string | undefined,
    isActive: undefined as boolean | undefined,
    teacherId: undefined as string | undefined
  })

  // Getters
  const filteredExams = computed(() => {
    let filtered = exams.value

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(exam => 
        exam.title.toLowerCase().includes(query) ||
        exam.description.toLowerCase().includes(query) ||
        exam.category.toLowerCase().includes(query)
      )
    }

    if (filters.value.category) {
      filtered = filtered.filter(exam => exam.category === filters.value.category)
    }

    if (filters.value.isActive !== undefined) {
      filtered = filtered.filter(exam => exam.isActive === filters.value.isActive)
    }

    if (filters.value.teacherId) {
      filtered = filtered.filter(exam => exam.teacherId === filters.value.teacherId)
    }

    return filtered
  })

  const examsByTeacher = computed(() => (teacherId: string) => 
    exams.value.filter(exam => exam.teacherId === teacherId)
  )

  const activeExams = computed(() => 
    exams.value.filter(exam => exam.isActive && exam.isPublished)
  )

  const examStats = computed(() => ({
    total: exams.value.length,
    active: exams.value.filter(e => e.isActive).length,
    published: exams.value.filter(e => e.isPublished).length,
    draft: exams.value.filter(e => !e.isPublished).length
  }))

  // Actions
  const loadExams = async (): Promise<void> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const savedExams = localStorage.getItem('lms_exams')
      if (savedExams) {
        exams.value = JSON.parse(savedExams).map((exam: any) => ({
          ...exam,
          createdAt: new Date(exam.createdAt),
          updatedAt: new Date(exam.updatedAt),
          availableFrom: exam.availableFrom ? new Date(exam.availableFrom) : undefined,
          availableUntil: exam.availableUntil ? new Date(exam.availableUntil) : undefined
        }))
      } else {
        // Initialize with sample exams
        exams.value = [
          {
            id: '1',
            title: 'Mathematics Quiz #1',
            description: 'Basic algebra and arithmetic quiz',
            teacherId: '2',
            questions: [
              {
                id: 'q1',
                type: 'multiple-choice',
                question: 'What is 2 + 2?',
                options: ['3', '4', '5', '6'],
                correctAnswer: 1,
                points: 10,
                difficulty: 'easy',
                tags: ['arithmetic'],
                required: true,
                order: 1
              },
              {
                id: 'q2',
                type: 'true-false',
                question: 'The square root of 16 is 4.',
                correctAnswer: 0, // true
                points: 10,
                difficulty: 'easy',
                tags: ['square-root'],
                required: true,
                order: 2
              }
            ],
            duration: 30,
            totalPoints: 20,
            passingScore: 70,
            maxAttempts: 3,
            isActive: true,
            isPublished: true,
            showResults: true,
            showCorrectAnswers: true,
            randomizeQuestions: false,
            randomizeOptions: false,
            allowReview: true,
            category: 'Mathematics',
            tags: ['quiz', 'basic'],
            instructions: 'Please read each question carefully and select the best answer.',
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date(),
            settings: {
              timeLimit: true,
              showTimer: true,
              preventCheating: false,
              fullScreen: false,
              disableCopy: false,
              shuffleQuestions: false
            }
          }
        ]
        saveExams()
      }
    } catch (error) {
      console.error('Error loading exams:', error)
    } finally {
      isLoading.value = false
    }
  }

  const createExam = async (examData: Omit<Exam, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Exam>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const newExam: Exam = {
        ...examData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      exams.value.push(newExam)
      saveExams()

      return {
        success: true,
        data: newExam,
        message: 'Exam created successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create exam'
      }
    } finally {
      isLoading.value = false
    }
  }

  const updateExam = async (id: string, examData: Partial<Exam>): Promise<ApiResponse<Exam>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const examIndex = exams.value.findIndex(e => e.id === id)
      if (examIndex === -1) {
        return {
          success: false,
          message: 'Exam not found'
        }
      }

      const updatedExam = {
        ...exams.value[examIndex],
        ...examData,
        updatedAt: new Date()
      }

      exams.value[examIndex] = updatedExam
      saveExams()

      return {
        success: true,
        data: updatedExam,
        message: 'Exam updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update exam'
      }
    } finally {
      isLoading.value = false
    }
  }

  const deleteExam = async (id: string): Promise<ApiResponse<void>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const examIndex = exams.value.findIndex(e => e.id === id)
      if (examIndex === -1) {
        return {
          success: false,
          message: 'Exam not found'
        }
      }

      exams.value.splice(examIndex, 1)
      saveExams()

      return {
        success: true,
        message: 'Exam deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to delete exam'
      }
    } finally {
      isLoading.value = false
    }
  }

  const publishExam = async (id: string): Promise<ApiResponse<Exam>> => {
    return updateExam(id, { isPublished: true, isActive: true })
  }

  const unpublishExam = async (id: string): Promise<ApiResponse<Exam>> => {
    return updateExam(id, { isPublished: false, isActive: false })
  }

  const duplicateExam = async (id: string): Promise<ApiResponse<Exam>> => {
    const exam = exams.value.find(e => e.id === id)
    if (!exam) {
      return {
        success: false,
        message: 'Exam not found'
      }
    }

    const duplicatedExam = {
      ...exam,
      title: `${exam.title} (Copy)`,
      isPublished: false,
      isActive: false
    }

    delete (duplicatedExam as any).id
    delete (duplicatedExam as any).createdAt
    delete (duplicatedExam as any).updatedAt

    return createExam(duplicatedExam)
  }

  const saveExams = () => {
    localStorage.setItem('lms_exams', JSON.stringify(exams.value))
  }

  const setCurrentExam = (exam: Exam | null) => {
    currentExam.value = exam
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    searchQuery.value = ''
    filters.value = {
      category: undefined,
      isActive: undefined,
      teacherId: undefined
    }
  }

  const getExamById = (id: string): Exam | undefined => {
    return exams.value.find(e => e.id === id)
  }

  return {
    // State
    exams,
    currentExam,
    submissions,
    questionBanks,
    isLoading,
    searchQuery,
    filters,
    
    // Getters
    filteredExams,
    examsByTeacher,
    activeExams,
    examStats,
    
    // Actions
    loadExams,
    createExam,
    updateExam,
    deleteExam,
    publishExam,
    unpublishExam,
    duplicateExam,
    setCurrentExam,
    setSearchQuery,
    setFilters,
    clearFilters,
    getExamById
  }
})
