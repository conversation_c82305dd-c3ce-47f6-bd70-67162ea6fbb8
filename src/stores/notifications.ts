import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Notification, Message, Announcement, ApiResponse } from '@/types'

export const useNotificationStore = defineStore('notifications', () => {
  // State
  const notifications = ref<Notification[]>([])
  const messages = ref<Message[]>([])
  const announcements = ref<Announcement[]>([])
  const isLoading = ref(false)

  // Getters
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead)
  )

  const unreadCount = computed(() => unreadNotifications.value.length)

  const notificationsByType = computed(() => ({
    exam: notifications.value.filter(n => n.type === 'exam'),
    grade: notifications.value.filter(n => n.type === 'grade'),
    announcement: notifications.value.filter(n => n.type === 'announcement'),
    message: notifications.value.filter(n => n.type === 'message'),
    system: notifications.value.filter(n => n.type === 'system')
  }))

  const unreadMessages = computed(() => 
    messages.value.filter(m => !m.isRead)
  )

  const activeAnnouncements = computed(() => 
    announcements.value.filter(a => 
      a.isPublished && 
      (!a.expiresAt || a.expiresAt > new Date())
    )
  )

  // Actions
  const loadNotifications = async (userId: string): Promise<void> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const savedNotifications = localStorage.getItem(`lms_notifications_${userId}`)
      if (savedNotifications) {
        notifications.value = JSON.parse(savedNotifications).map((n: any) => ({
          ...n,
          createdAt: new Date(n.createdAt),
          expiresAt: n.expiresAt ? new Date(n.expiresAt) : undefined
        }))
      } else {
        // Initialize with sample notifications
        notifications.value = [
          {
            id: '1',
            userId,
            type: 'exam',
            title: 'New Exam Available',
            message: 'Mathematics Quiz #1 is now available for taking.',
            isRead: false,
            priority: 'medium',
            actionUrl: '/student/exam/1',
            createdAt: new Date()
          },
          {
            id: '2',
            userId,
            type: 'system',
            title: 'Welcome to LMS',
            message: 'Welcome to our Learning Management System. Get started by exploring your dashboard.',
            isRead: false,
            priority: 'low',
            createdAt: new Date(Date.now() - 86400000) // 1 day ago
          }
        ]
        saveNotifications(userId)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      isLoading.value = false
    }
  }

  const loadMessages = async (userId: string): Promise<void> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const savedMessages = localStorage.getItem(`lms_messages_${userId}`)
      if (savedMessages) {
        messages.value = JSON.parse(savedMessages).map((m: any) => ({
          ...m,
          createdAt: new Date(m.createdAt)
        }))
      }
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      isLoading.value = false
    }
  }

  const loadAnnouncements = async (): Promise<void> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const savedAnnouncements = localStorage.getItem('lms_announcements')
      if (savedAnnouncements) {
        announcements.value = JSON.parse(savedAnnouncements).map((a: any) => ({
          ...a,
          createdAt: new Date(a.createdAt),
          updatedAt: new Date(a.updatedAt),
          expiresAt: a.expiresAt ? new Date(a.expiresAt) : undefined
        }))
      } else {
        // Initialize with sample announcements
        announcements.value = [
          {
            id: '1',
            authorId: '1',
            title: 'System Maintenance Notice',
            content: 'The system will undergo maintenance on Sunday from 2 AM to 4 AM. Please save your work before this time.',
            targetRoles: ['admin', 'teacher', 'student'],
            isPublished: true,
            priority: 'high',
            expiresAt: new Date(Date.now() + 7 * 86400000), // 7 days from now
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
        saveAnnouncements()
      }
    } catch (error) {
      console.error('Error loading announcements:', error)
    } finally {
      isLoading.value = false
    }
  }

  const createNotification = async (notificationData: Omit<Notification, 'id' | 'createdAt'>): Promise<ApiResponse<Notification>> => {
    try {
      const newNotification: Notification = {
        ...notificationData,
        id: Date.now().toString(),
        createdAt: new Date()
      }

      notifications.value.unshift(newNotification)
      saveNotifications(notificationData.userId)

      return {
        success: true,
        data: newNotification,
        message: 'Notification created successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create notification'
      }
    }
  }

  const markAsRead = async (notificationId: string, userId: string): Promise<ApiResponse<void>> => {
    try {
      const notification = notifications.value.find(n => n.id === notificationId)
      if (!notification) {
        return {
          success: false,
          message: 'Notification not found'
        }
      }

      notification.isRead = true
      saveNotifications(userId)

      return {
        success: true,
        message: 'Notification marked as read'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to mark notification as read'
      }
    }
  }

  const markAllAsRead = async (userId: string): Promise<ApiResponse<void>> => {
    try {
      notifications.value.forEach(n => {
        if (n.userId === userId) {
          n.isRead = true
        }
      })
      saveNotifications(userId)

      return {
        success: true,
        message: 'All notifications marked as read'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to mark all notifications as read'
      }
    }
  }

  const deleteNotification = async (notificationId: string, userId: string): Promise<ApiResponse<void>> => {
    try {
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index === -1) {
        return {
          success: false,
          message: 'Notification not found'
        }
      }

      notifications.value.splice(index, 1)
      saveNotifications(userId)

      return {
        success: true,
        message: 'Notification deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to delete notification'
      }
    }
  }

  const sendMessage = async (messageData: Omit<Message, 'id' | 'createdAt'>): Promise<ApiResponse<Message>> => {
    try {
      const newMessage: Message = {
        ...messageData,
        id: Date.now().toString(),
        createdAt: new Date()
      }

      messages.value.unshift(newMessage)
      saveMessages(messageData.fromUserId)
      saveMessages(messageData.toUserId)

      // Create notification for recipient
      await createNotification({
        userId: messageData.toUserId,
        type: 'message',
        title: 'New Message',
        message: `You have a new message: ${messageData.subject}`,
        isRead: false,
        priority: 'medium',
        actionUrl: '/messages'
      })

      return {
        success: true,
        data: newMessage,
        message: 'Message sent successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send message'
      }
    }
  }

  const createAnnouncement = async (announcementData: Omit<Announcement, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> => {
    try {
      const newAnnouncement: Announcement = {
        ...announcementData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      announcements.value.unshift(newAnnouncement)
      saveAnnouncements()

      return {
        success: true,
        data: newAnnouncement,
        message: 'Announcement created successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create announcement'
      }
    }
  }

  // Utility functions for creating specific notifications
  const notifyExamAvailable = (userId: string, examTitle: string, examId: string) => {
    return createNotification({
      userId,
      type: 'exam',
      title: 'New Exam Available',
      message: `${examTitle} is now available for taking.`,
      isRead: false,
      priority: 'medium',
      actionUrl: `/student/exam/${examId}`
    })
  }

  const notifyGradePosted = (userId: string, examTitle: string, score: number) => {
    return createNotification({
      userId,
      type: 'grade',
      title: 'Grade Posted',
      message: `Your grade for ${examTitle} has been posted. Score: ${score}%`,
      isRead: false,
      priority: 'medium',
      actionUrl: '/student/results'
    })
  }

  const saveNotifications = (userId: string) => {
    const userNotifications = notifications.value.filter(n => n.userId === userId)
    localStorage.setItem(`lms_notifications_${userId}`, JSON.stringify(userNotifications))
  }

  const saveMessages = (userId: string) => {
    const userMessages = messages.value.filter(m => m.fromUserId === userId || m.toUserId === userId)
    localStorage.setItem(`lms_messages_${userId}`, JSON.stringify(userMessages))
  }

  const saveAnnouncements = () => {
    localStorage.setItem('lms_announcements', JSON.stringify(announcements.value))
  }

  return {
    // State
    notifications,
    messages,
    announcements,
    isLoading,
    
    // Getters
    unreadNotifications,
    unreadCount,
    notificationsByType,
    unreadMessages,
    activeAnnouncements,
    
    // Actions
    loadNotifications,
    loadMessages,
    loadAnnouncements,
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    sendMessage,
    createAnnouncement,
    notifyExamAvailable,
    notifyGradePosted
  }
})
