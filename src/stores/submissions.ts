import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ExamSubmission, Answer, ApiResponse, ExamAnalytics, QuestionAnalytics } from '@/types'
import { useExamStore } from './exams'

export const useSubmissionStore = defineStore('submissions', () => {
  // State
  const submissions = ref<ExamSubmission[]>([])
  const currentSubmission = ref<ExamSubmission | null>(null)
  const isLoading = ref(false)
  const examInProgress = ref<{
    examId: string
    answers: Answer[]
    startTime: Date
    timeSpent: number
  } | null>(null)

  // Getters
  const submissionsByExam = computed(() => (examId: string) => 
    submissions.value.filter(sub => sub.examId === examId)
  )

  const submissionsByStudent = computed(() => (studentId: string) => 
    submissions.value.filter(sub => sub.studentId === studentId)
  )

  const pendingGrading = computed(() => 
    submissions.value.filter(sub => sub.isCompleted && !sub.isGraded)
  )

  const submissionStats = computed(() => ({
    total: submissions.value.length,
    completed: submissions.value.filter(s => s.isCompleted).length,
    graded: submissions.value.filter(s => s.isGraded).length,
    pending: pendingGrading.value.length,
    inProgress: submissions.value.filter(s => s.status === 'in-progress').length
  }))

  // Actions
  const loadSubmissions = async (): Promise<void> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const savedSubmissions = localStorage.getItem('lms_submissions')
      if (savedSubmissions) {
        submissions.value = JSON.parse(savedSubmissions).map((sub: any) => ({
          ...sub,
          startedAt: new Date(sub.startedAt),
          submittedAt: sub.submittedAt ? new Date(sub.submittedAt) : undefined,
          gradedAt: sub.gradedAt ? new Date(sub.gradedAt) : undefined
        }))
      }
    } catch (error) {
      console.error('Error loading submissions:', error)
    } finally {
      isLoading.value = false
    }
  }

  const startExam = async (examId: string, studentId: string): Promise<ApiResponse<ExamSubmission>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // Check if student already has an in-progress submission
      const existingSubmission = submissions.value.find(
        sub => sub.examId === examId && sub.studentId === studentId && sub.status === 'in-progress'
      )

      if (existingSubmission) {
        currentSubmission.value = existingSubmission
        examInProgress.value = {
          examId,
          answers: existingSubmission.answers,
          startTime: existingSubmission.startedAt,
          timeSpent: existingSubmission.timeSpent
        }
        return {
          success: true,
          data: existingSubmission,
          message: 'Resumed existing exam session'
        }
      }

      // Create new submission
      const newSubmission: ExamSubmission = {
        id: Date.now().toString(),
        examId,
        studentId,
        answers: [],
        isGraded: false,
        isCompleted: false,
        attemptNumber: getAttemptNumber(examId, studentId) + 1,
        startedAt: new Date(),
        timeSpent: 0,
        status: 'in-progress'
      }

      submissions.value.push(newSubmission)
      currentSubmission.value = newSubmission
      examInProgress.value = {
        examId,
        answers: [],
        startTime: new Date(),
        timeSpent: 0
      }
      
      saveSubmissions()

      return {
        success: true,
        data: newSubmission,
        message: 'Exam started successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to start exam'
      }
    } finally {
      isLoading.value = false
    }
  }

  const saveAnswer = async (questionId: string, answer: string | number | string[]): Promise<void> => {
    if (!currentSubmission.value || !examInProgress.value) return

    const answerData: Answer = {
      questionId,
      answer,
      timeSpent: Math.floor((Date.now() - examInProgress.value.startTime.getTime()) / 1000)
    }

    // Update or add answer
    const existingIndex = examInProgress.value.answers.findIndex(a => a.questionId === questionId)
    if (existingIndex >= 0) {
      examInProgress.value.answers[existingIndex] = answerData
    } else {
      examInProgress.value.answers.push(answerData)
    }

    // Update submission
    currentSubmission.value.answers = [...examInProgress.value.answers]
    currentSubmission.value.timeSpent = Math.floor((Date.now() - examInProgress.value.startTime.getTime()) / 1000)

    // Update in submissions array
    const submissionIndex = submissions.value.findIndex(s => s.id === currentSubmission.value?.id)
    if (submissionIndex >= 0) {
      submissions.value[submissionIndex] = { ...currentSubmission.value }
    }

    saveSubmissions()
  }

  const submitExam = async (): Promise<ApiResponse<ExamSubmission>> => {
    if (!currentSubmission.value || !examInProgress.value) {
      return {
        success: false,
        message: 'No exam in progress'
      }
    }

    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))

      const submittedSubmission = {
        ...currentSubmission.value,
        isCompleted: true,
        submittedAt: new Date(),
        status: 'submitted' as const,
        timeSpent: Math.floor((Date.now() - examInProgress.value.startTime.getTime()) / 1000)
      }

      // Auto-grade if possible
      const gradedSubmission = await autoGradeSubmission(submittedSubmission)

      // Update submission in array
      const submissionIndex = submissions.value.findIndex(s => s.id === submittedSubmission.id)
      if (submissionIndex >= 0) {
        submissions.value[submissionIndex] = gradedSubmission
      }

      currentSubmission.value = null
      examInProgress.value = null
      saveSubmissions()

      return {
        success: true,
        data: gradedSubmission,
        message: 'Exam submitted successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit exam'
      }
    } finally {
      isLoading.value = false
    }
  }

  const gradeSubmission = async (
    submissionId: string, 
    score: number, 
    feedback?: string,
    gradedBy?: string
  ): Promise<ApiResponse<ExamSubmission>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))

      const submissionIndex = submissions.value.findIndex(s => s.id === submissionId)
      if (submissionIndex === -1) {
        return {
          success: false,
          message: 'Submission not found'
        }
      }

      const submission = submissions.value[submissionIndex]
      const examStore = useExamStore()
      const exam = examStore.getExamById(submission.examId)
      
      if (!exam) {
        return {
          success: false,
          message: 'Exam not found'
        }
      }

      const percentage = Math.round((score / exam.totalPoints) * 100)
      let grade = 'F'
      if (percentage >= 90) grade = 'A'
      else if (percentage >= 80) grade = 'B'
      else if (percentage >= 70) grade = 'C'
      else if (percentage >= 60) grade = 'D'

      const gradedSubmission = {
        ...submission,
        score,
        percentage,
        grade,
        feedback,
        isGraded: true,
        gradedAt: new Date(),
        gradedBy,
        status: 'graded' as const
      }

      submissions.value[submissionIndex] = gradedSubmission
      saveSubmissions()

      return {
        success: true,
        data: gradedSubmission,
        message: 'Submission graded successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to grade submission'
      }
    } finally {
      isLoading.value = false
    }
  }

  const autoGradeSubmission = async (submission: ExamSubmission): Promise<ExamSubmission> => {
    const examStore = useExamStore()
    const exam = examStore.getExamById(submission.examId)
    
    if (!exam) return submission

    let totalScore = 0
    
    for (const question of exam.questions) {
      const answer = submission.answers.find(a => a.questionId === question.id)
      if (!answer) continue

      // Auto-grade multiple choice and true/false questions
      if (question.type === 'multiple-choice' || question.type === 'true-false') {
        if (answer.answer === question.correctAnswer) {
          totalScore += question.points
        }
      }
      // For other types, manual grading is required
    }

    const percentage = Math.round((totalScore / exam.totalPoints) * 100)
    let grade = 'F'
    if (percentage >= 90) grade = 'A'
    else if (percentage >= 80) grade = 'B'
    else if (percentage >= 70) grade = 'C'
    else if (percentage >= 60) grade = 'D'

    return {
      ...submission,
      score: totalScore,
      percentage,
      grade,
      isGraded: true,
      gradedAt: new Date(),
      status: 'graded'
    }
  }

  const getAttemptNumber = (examId: string, studentId: string): number => {
    const studentSubmissions = submissions.value.filter(
      sub => sub.examId === examId && sub.studentId === studentId
    )
    return studentSubmissions.length
  }

  const getExamAnalytics = (examId: string): ExamAnalytics => {
    const examSubmissions = submissionsByExam.value(examId)
    const completedSubmissions = examSubmissions.filter(s => s.isCompleted)
    
    if (completedSubmissions.length === 0) {
      return {
        examId,
        totalSubmissions: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        passRate: 0,
        averageTimeSpent: 0,
        questionAnalytics: [],
        difficultyDistribution: { easy: 0, medium: 0, hard: 0 },
        submissionTrends: []
      }
    }

    const scores = completedSubmissions.map(s => s.score || 0)
    const averageScore = scores.reduce((a, b) => a + b, 0) / scores.length
    const highestScore = Math.max(...scores)
    const lowestScore = Math.min(...scores)
    const passRate = (completedSubmissions.filter(s => (s.percentage || 0) >= 70).length / completedSubmissions.length) * 100
    const averageTimeSpent = completedSubmissions.reduce((sum, s) => sum + s.timeSpent, 0) / completedSubmissions.length

    return {
      examId,
      totalSubmissions: completedSubmissions.length,
      averageScore,
      highestScore,
      lowestScore,
      passRate,
      averageTimeSpent,
      questionAnalytics: [],
      difficultyDistribution: { easy: 0, medium: 0, hard: 0 },
      submissionTrends: []
    }
  }

  const saveSubmissions = () => {
    localStorage.setItem('lms_submissions', JSON.stringify(submissions.value))
  }

  return {
    // State
    submissions,
    currentSubmission,
    isLoading,
    examInProgress,
    
    // Getters
    submissionsByExam,
    submissionsByStudent,
    pendingGrading,
    submissionStats,
    
    // Actions
    loadSubmissions,
    startExam,
    saveAnswer,
    submitExam,
    gradeSubmission,
    getAttemptNumber,
    getExamAnalytics
  }
})
