import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserState, ApiResponse } from '@/types'

export const useUserStore = defineStore('users', () => {
  // State
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const isLoading = ref(false)
  const searchQuery = ref('')
  const filters = ref({
    role: undefined as 'admin' | 'teacher' | 'student' | undefined,
    isActive: undefined as boolean | undefined,
    department: undefined as string | undefined
  })

  // Getters
  const filteredUsers = computed(() => {
    let filtered = users.value

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.firstName.toLowerCase().includes(query) ||
        user.lastName.toLowerCase().includes(query)
      )
    }

    // Role filter
    if (filters.value.role) {
      filtered = filtered.filter(user => user.role === filters.value.role)
    }

    // Active status filter
    if (filters.value.isActive !== undefined) {
      filtered = filtered.filter(user => user.isActive === filters.value.isActive)
    }

    // Department filter
    if (filters.value.department) {
      filtered = filtered.filter(user => 
        user.metadata?.department === filters.value.department
      )
    }

    return filtered
  })

  const usersByRole = computed(() => ({
    admins: users.value.filter(u => u.role === 'admin'),
    teachers: users.value.filter(u => u.role === 'teacher'),
    students: users.value.filter(u => u.role === 'student')
  }))

  const userStats = computed(() => ({
    total: users.value.length,
    active: users.value.filter(u => u.isActive).length,
    inactive: users.value.filter(u => !u.isActive).length,
    admins: usersByRole.value.admins.length,
    teachers: usersByRole.value.teachers.length,
    students: usersByRole.value.students.length
  }))

  // Actions
  const loadUsers = async (): Promise<void> => {
    isLoading.value = true
    try {
      // Simulate API call - in real app, fetch from backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Load mock data from localStorage or use defaults
      const savedUsers = localStorage.getItem('lms_users')
      if (savedUsers) {
        users.value = JSON.parse(savedUsers)
      } else {
        // Initialize with default users
        users.value = [
          {
            id: '1',
            email: '<EMAIL>',
            name: 'System Administrator',
            firstName: 'System',
            lastName: 'Administrator',
            role: 'admin',
            isActive: true,
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date()
          },
          {
            id: '2',
            email: '<EMAIL>',
            name: 'John Smith',
            firstName: 'John',
            lastName: 'Smith',
            role: 'teacher',
            isActive: true,
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date(),
            metadata: {
              teacherId: 'T001',
              department: 'Mathematics',
              subjects: ['Algebra', 'Calculus', 'Statistics']
            }
          },
          {
            id: '3',
            email: '<EMAIL>',
            name: 'Jane Doe',
            firstName: 'Jane',
            lastName: 'Doe',
            role: 'student',
            isActive: true,
            createdAt: new Date('2024-02-01'),
            updatedAt: new Date(),
            metadata: {
              studentId: 'S001',
              grade: '10th Grade'
            }
          }
        ]
        saveUsers()
      }
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      isLoading.value = false
    }
  }

  const createUser = async (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> => {
    isLoading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Check if email already exists
      if (users.value.some(u => u.email === userData.email)) {
        return {
          success: false,
          message: 'Email already exists'
        }
      }

      const newUser: User = {
        ...userData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      users.value.push(newUser)
      saveUsers()

      return {
        success: true,
        data: newUser,
        message: 'User created successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create user'
      }
    } finally {
      isLoading.value = false
    }
  }

  const updateUser = async (id: string, userData: Partial<User>): Promise<ApiResponse<User>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const userIndex = users.value.findIndex(u => u.id === id)
      if (userIndex === -1) {
        return {
          success: false,
          message: 'User not found'
        }
      }

      const updatedUser = {
        ...users.value[userIndex],
        ...userData,
        updatedAt: new Date()
      }

      users.value[userIndex] = updatedUser
      saveUsers()

      return {
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update user'
      }
    } finally {
      isLoading.value = false
    }
  }

  const deleteUser = async (id: string): Promise<ApiResponse<void>> => {
    isLoading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const userIndex = users.value.findIndex(u => u.id === id)
      if (userIndex === -1) {
        return {
          success: false,
          message: 'User not found'
        }
      }

      users.value.splice(userIndex, 1)
      saveUsers()

      return {
        success: true,
        message: 'User deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to delete user'
      }
    } finally {
      isLoading.value = false
    }
  }

  const toggleUserStatus = async (id: string): Promise<ApiResponse<User>> => {
    const user = users.value.find(u => u.id === id)
    if (!user) {
      return {
        success: false,
        message: 'User not found'
      }
    }

    return updateUser(id, { isActive: !user.isActive })
  }

  const saveUsers = () => {
    localStorage.setItem('lms_users', JSON.stringify(users.value))
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    searchQuery.value = ''
    filters.value = {
      role: undefined,
      isActive: undefined,
      department: undefined
    }
  }

  const getUserById = (id: string): User | undefined => {
    return users.value.find(u => u.id === id)
  }

  return {
    // State
    users,
    currentUser,
    isLoading,
    searchQuery,
    filters,
    
    // Getters
    filteredUsers,
    usersByRole,
    userStats,
    
    // Actions
    loadUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    setSearchQuery,
    setFilters,
    clearFilters,
    getUserById
  }
})
