// User Management Types
export interface User {
  id: string
  email: string
  name: string
  firstName: string
  lastName: string
  role: 'admin' | 'teacher' | 'student'
  avatar?: string
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
  metadata?: {
    department?: string
    studentId?: string
    teacherId?: string
    grade?: string
    subjects?: string[]
  }
}

// Question and Exam Types
export interface Question {
  id: string
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay' | 'fill-blank'
  question: string
  options?: string[] // For multiple choice
  correctAnswer?: string | number | string[] // Support multiple correct answers
  points: number
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  explanation?: string
  timeLimit?: number // in seconds
  required: boolean
  order: number
}

export interface QuestionBank {
  id: string
  name: string
  description: string
  teacherId: string
  questions: Question[]
  tags: string[]
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Exam {
  id: string
  title: string
  description: string
  teacherId: string
  questions: Question[]
  duration: number // in minutes
  totalPoints: number
  passingScore: number
  maxAttempts: number
  isActive: boolean
  isPublished: boolean
  availableFrom?: Date
  availableUntil?: Date
  showResults: boolean
  showCorrectAnswers: boolean
  randomizeQuestions: boolean
  randomizeOptions: boolean
  allowReview: boolean
  category: string
  tags: string[]
  instructions?: string
  createdAt: Date
  updatedAt: Date
  settings: {
    timeLimit: boolean
    showTimer: boolean
    preventCheating: boolean
    fullScreen: boolean
    disableCopy: boolean
    shuffleQuestions: boolean
  }
}

// Submission and Grading Types
export interface Answer {
  questionId: string
  answer: string | number | string[]
  timeSpent?: number // in seconds
  flagged?: boolean
  confidence?: number // 1-5 scale
}

export interface ExamSubmission {
  id: string
  examId: string
  studentId: string
  answers: Answer[]
  score?: number
  percentage?: number
  grade?: string
  feedback?: string
  isGraded: boolean
  isCompleted: boolean
  attemptNumber: number
  startedAt: Date
  submittedAt?: Date
  gradedAt?: Date
  gradedBy?: string
  timeSpent: number // in seconds
  status: 'in-progress' | 'submitted' | 'graded' | 'expired'
  metadata?: {
    ipAddress?: string
    userAgent?: string
    violations?: string[]
  }
}

export interface GradingRubric {
  id: string
  questionId: string
  criteria: {
    id: string
    description: string
    points: number
    levels: {
      level: string
      description: string
      points: number
    }[]
  }[]
}

// Notification and Communication Types
export interface Notification {
  id: string
  userId: string
  type: 'exam' | 'grade' | 'announcement' | 'message' | 'system'
  title: string
  message: string
  isRead: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  actionUrl?: string
  createdAt: Date
  expiresAt?: Date
}

export interface Message {
  id: string
  fromUserId: string
  toUserId: string
  subject: string
  content: string
  isRead: boolean
  parentMessageId?: string
  attachments?: string[]
  createdAt: Date
}

export interface Announcement {
  id: string
  authorId: string
  title: string
  content: string
  targetRoles: ('admin' | 'teacher' | 'student')[]
  targetUsers?: string[]
  isPublished: boolean
  priority: 'low' | 'medium' | 'high'
  expiresAt?: Date
  createdAt: Date
  updatedAt: Date
}

// Analytics and Reporting Types
export interface ExamAnalytics {
  examId: string
  totalSubmissions: number
  averageScore: number
  highestScore: number
  lowestScore: number
  passRate: number
  averageTimeSpent: number
  questionAnalytics: QuestionAnalytics[]
  difficultyDistribution: {
    easy: number
    medium: number
    hard: number
  }
  submissionTrends: {
    date: string
    count: number
  }[]
}

export interface QuestionAnalytics {
  questionId: string
  correctAnswers: number
  incorrectAnswers: number
  averageTimeSpent: number
  difficultyIndex: number
  discriminationIndex: number
  optionAnalytics?: {
    option: string
    count: number
    percentage: number
  }[]
}

export interface StudentProgress {
  studentId: string
  totalExams: number
  completedExams: number
  averageScore: number
  improvementTrend: number
  strongSubjects: string[]
  weakSubjects: string[]
  recentActivity: {
    examId: string
    examTitle: string
    score: number
    completedAt: Date
  }[]
}

// Store State Interfaces
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface UserState {
  users: User[]
  currentUser: User | null
  isLoading: boolean
  searchQuery: string
  filters: {
    role?: 'admin' | 'teacher' | 'student'
    isActive?: boolean
    department?: string
  }
}

export interface ExamState {
  exams: Exam[]
  currentExam: Exam | null
  submissions: ExamSubmission[]
  questionBanks: QuestionBank[]
  isLoading: boolean
  searchQuery: string
  filters: {
    category?: string
    isActive?: boolean
    teacherId?: string
  }
}

export interface NotificationState {
  notifications: Notification[]
  unreadCount: number
  isLoading: boolean
}

export interface SystemState {
  announcements: Announcement[]
  messages: Message[]
  analytics: {
    totalUsers: number
    totalExams: number
    totalSubmissions: number
    systemHealth: 'good' | 'warning' | 'critical'
  }
  isLoading: boolean
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Form and UI Types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file'
  required: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    custom?: (value: any) => boolean | string
  }
}

export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: any) => string
}
