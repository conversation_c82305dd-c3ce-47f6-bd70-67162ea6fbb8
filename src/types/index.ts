export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'teacher' | 'student'
  createdAt: Date
  updatedAt: Date
}

export interface Question {
  id: string
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay'
  question: string
  options?: string[] // For multiple choice
  correctAnswer?: string | number // For multiple choice and true/false
  points: number
}

export interface Exam {
  id: string
  title: string
  description: string
  teacherId: string
  questions: Question[]
  duration: number // in minutes
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Answer {
  questionId: string
  answer: string | number
}

export interface ExamSubmission {
  id: string
  examId: string
  studentId: string
  answers: Answer[]
  score?: number
  isGraded: boolean
  submittedAt: Date
  gradedAt?: Date
  gradedBy?: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface ExamState {
  exams: Exam[]
  currentExam: Exam | null
  submissions: ExamSubmission[]
  isLoading: boolean
}

export interface UserState {
  users: User[]
  isLoading: boolean
}
