<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">Admin Dashboard</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">Welcome, {{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <div v-else>
          <!-- Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="card">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-sm font-medium text-gray-500">Total Users</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ stats.totalUsers }}</p>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-sm font-medium text-gray-500">Active Exams</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ stats.activeExams }}</p>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-sm font-medium text-gray-500">Students</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ stats.activeStudents }}</p>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                      <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-sm font-medium text-gray-500">Pending Grading</h3>
                  <p class="text-2xl font-bold text-gray-900">{{ stats.pendingGrading }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Management Actions -->
            <div>
              <h2 class="text-lg font-medium text-gray-900 mb-4">Management</h2>
              <div class="space-y-4">
                <router-link to="/admin/users" class="card hover:shadow-lg transition-shadow cursor-pointer block">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-medium text-gray-900">User Management</h3>
                      <p class="text-gray-600">Manage teachers, students, and administrators</p>
                    </div>
                  </div>
                </router-link>

                <div class="card hover:shadow-lg transition-shadow cursor-pointer">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-medium text-gray-900">System Analytics</h3>
                      <p class="text-gray-600">View detailed reports and analytics</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div>
              <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
              <div class="card">
                <div class="space-y-4">
                  <div v-for="activity in recentActivity" :key="activity.message" class="flex items-start">
                    <div class="flex-shrink-0">
                      <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm text-gray-900">{{ activity.message }}</p>
                      <p class="text-xs text-gray-500">{{ activity.time }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- System Status -->
          <div class="mt-8">
            <div class="card">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">System Status</h3>
                  <p class="text-sm text-gray-600">All systems operational</p>
                </div>
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                  <span class="text-sm font-medium text-green-600">Healthy</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/users'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'
import { useNotificationStore } from '@/stores/notifications'

const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()
const notificationStore = useNotificationStore()

const isLoading = ref(true)

const stats = computed(() => ({
  totalUsers: userStore.userStats.total,
  totalExams: examStore.examStats.total,
  activeStudents: userStore.userStats.students,
  totalSubmissions: submissionStore.submissionStats.total,
  pendingGrading: submissionStore.submissionStats.pending,
  activeExams: examStore.examStats.active,
  totalTeachers: userStore.userStats.teachers,
  systemHealth: 'good' as const
}))

const recentActivity = ref([
  { type: 'user', message: 'New student registered: Jane Smith', time: '2 hours ago' },
  { type: 'exam', message: 'Math Quiz #2 published by John Teacher', time: '4 hours ago' },
  { type: 'submission', message: '15 new exam submissions received', time: '6 hours ago' },
  { type: 'system', message: 'System backup completed successfully', time: '1 day ago' }
])

const logout = () => {
  authStore.logout()
  router.push('/login')
}

const refreshData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      userStore.loadUsers(),
      examStore.loadExams(),
      submissionStore.loadSubmissions(),
      notificationStore.loadAnnouncements()
    ])
  } catch (error) {
    console.error('Error refreshing data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await refreshData()
})
</script>