<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/admin" class="text-blue-600 hover:text-blue-800 mr-4">← Back</router-link>
            <h1 class="text-xl font-semibold text-gray-900">User Management</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Header with Actions -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h2 class="text-2xl font-bold text-gray-900">User Management</h2>
            <p class="text-gray-600">Manage teachers, students, and administrators</p>
          </div>
          <button @click="showCreateModal = true" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add User
          </button>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Total Users</h3>
            <p class="text-2xl font-bold text-gray-900">{{ userStore.userStats.total }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Administrators</h3>
            <p class="text-2xl font-bold text-blue-600">{{ userStore.userStats.admins }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Teachers</h3>
            <p class="text-2xl font-bold text-green-600">{{ userStore.userStats.teachers }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Students</h3>
            <p class="text-2xl font-bold text-purple-600">{{ userStore.userStats.students }}</p>
          </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="form-label">Search Users</label>
              <input
                v-model="userStore.searchQuery"
                type="text"
                placeholder="Search by name or email..."
                class="form-input"
              />
            </div>
            <div>
              <label class="form-label">Role</label>
              <select v-model="userStore.filters.role" class="form-input">
                <option value="">All Roles</option>
                <option value="admin">Administrator</option>
                <option value="teacher">Teacher</option>
                <option value="student">Student</option>
              </select>
            </div>
            <div>
              <label class="form-label">Status</label>
              <select v-model="userStore.filters.isActive" class="form-input">
                <option :value="undefined">All Status</option>
                <option :value="true">Active</option>
                <option :value="false">Inactive</option>
              </select>
            </div>
            <div class="flex items-end">
              <button @click="userStore.clearFilters()" class="btn-secondary w-full">
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        <!-- Users Table -->
        <div class="card">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="user in userStore.filteredUsers" :key="user.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700">
                            {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="getRoleBadgeClass(user.role)">
                      {{ user.role.charAt(0).toUpperCase() + user.role.slice(1) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                      {{ user.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(user.createdAt) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button @click="editUser(user)" class="text-blue-600 hover:text-blue-900">
                      Edit
                    </button>
                    <button @click="toggleUserStatus(user)"
                            :class="user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'">
                      {{ user.isActive ? 'Deactivate' : 'Activate' }}
                    </button>
                    <button @click="deleteUser(user)" class="text-red-600 hover:text-red-900">
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div v-if="userStore.filteredUsers.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
            <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        </div>
      </div>
    </main>

    <!-- Create/Edit User Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? 'Create New User' : 'Edit User' }}
          </h3>

          <form @submit.prevent="saveUser" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="form-label">First Name</label>
                <input v-model="userForm.firstName" type="text" required class="form-input" />
              </div>
              <div>
                <label class="form-label">Last Name</label>
                <input v-model="userForm.lastName" type="text" required class="form-input" />
              </div>
            </div>

            <div>
              <label class="form-label">Email</label>
              <input v-model="userForm.email" type="email" required class="form-input" />
            </div>

            <div>
              <label class="form-label">Role</label>
              <select v-model="userForm.role" required class="form-input">
                <option value="">Select Role</option>
                <option value="admin">Administrator</option>
                <option value="teacher">Teacher</option>
                <option value="student">Student</option>
              </select>
            </div>

            <div v-if="userForm.role === 'teacher'">
              <label class="form-label">Department</label>
              <input v-model="userForm.department" type="text" class="form-input" />
            </div>

            <div v-if="userForm.role === 'student'">
              <label class="form-label">Grade</label>
              <input v-model="userForm.grade" type="text" class="form-input" />
            </div>

            <div class="flex items-center">
              <input v-model="userForm.isActive" type="checkbox" class="h-4 w-4 text-blue-600" />
              <label class="ml-2 text-sm text-gray-700">Active User</label>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="closeModal" class="btn-secondary">Cancel</button>
              <button type="submit" :disabled="userStore.isLoading" class="btn-primary">
                {{ userStore.isLoading ? 'Saving...' : 'Save User' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Delete</h3>
          <p class="text-sm text-gray-500 mb-6">
            Are you sure you want to delete {{ userToDelete?.name }}? This action cannot be undone.
          </p>
          <div class="flex justify-center space-x-3">
            <button @click="showDeleteModal = false" class="btn-secondary">Cancel</button>
            <button @click="confirmDelete" :disabled="userStore.isLoading" class="btn-danger">
              {{ userStore.isLoading ? 'Deleting...' : 'Delete' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/users'
import type { User } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()

// Modal states
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const userToDelete = ref<User | null>(null)

// Form data
const userForm = ref({
  firstName: '',
  lastName: '',
  email: '',
  role: '' as 'admin' | 'teacher' | 'student' | '',
  department: '',
  grade: '',
  isActive: true
})

const editingUserId = ref<string | null>(null)

// Methods
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const getRoleBadgeClass = (role: string) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800'
    case 'teacher':
      return 'bg-blue-100 text-blue-800'
    case 'student':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}

const editUser = (user: User) => {
  userForm.value = {
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    role: user.role,
    department: user.metadata?.department || '',
    grade: user.metadata?.grade || '',
    isActive: user.isActive
  }
  editingUserId.value = user.id
  showEditModal.value = true
}

const deleteUser = (user: User) => {
  userToDelete.value = user
  showDeleteModal.value = true
}

const toggleUserStatus = async (user: User) => {
  const result = await userStore.toggleUserStatus(user.id)
  if (result.success) {
    console.log('User status updated successfully')
  } else {
    console.error('Failed to update user status:', result.message)
  }
}

const saveUser = async () => {
  const userData = {
    name: `${userForm.value.firstName} ${userForm.value.lastName}`,
    firstName: userForm.value.firstName,
    lastName: userForm.value.lastName,
    email: userForm.value.email,
    role: userForm.value.role as 'admin' | 'teacher' | 'student',
    isActive: userForm.value.isActive,
    metadata: {
      ...(userForm.value.role === 'teacher' && { department: userForm.value.department }),
      ...(userForm.value.role === 'student' && { grade: userForm.value.grade })
    }
  }

  let result
  if (editingUserId.value) {
    result = await userStore.updateUser(editingUserId.value, userData)
  } else {
    result = await userStore.createUser(userData)
  }

  if (result.success) {
    closeModal()
    console.log('User saved successfully')
  } else {
    console.error('Failed to save user:', result.message)
  }
}

const confirmDelete = async () => {
  if (!userToDelete.value) return

  const result = await userStore.deleteUser(userToDelete.value.id)
  if (result.success) {
    showDeleteModal.value = false
    userToDelete.value = null
    console.log('User deleted successfully')
  } else {
    console.error('Failed to delete user:', result.message)
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingUserId.value = null
  userForm.value = {
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    department: '',
    grade: '',
    isActive: true
  }
}

onMounted(async () => {
  await userStore.loadUsers()
})
</script>