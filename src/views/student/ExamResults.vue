<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/student" class="text-blue-600 hover:text-blue-800 mr-4">← Back</router-link>
            <h1 class="text-xl font-semibold text-gray-900">Exam Results</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Header -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Exam Results</h2>
          <p class="text-gray-600">View your completed exams and performance analytics</p>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <!-- Performance Overview -->
        <div v-else>
          <!-- Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="card">
              <h3 class="text-sm font-medium text-gray-500">Completed Exams</h3>
              <p class="text-2xl font-bold text-gray-900">{{ completedSubmissions.length }}</p>
            </div>
            <div class="card">
              <h3 class="text-sm font-medium text-gray-500">Average Score</h3>
              <p class="text-2xl font-bold text-blue-600">{{ averageScore }}%</p>
            </div>
            <div class="card">
              <h3 class="text-sm font-medium text-gray-500">Highest Score</h3>
              <p class="text-2xl font-bold text-green-600">{{ highestScore }}%</p>
            </div>
            <div class="card">
              <h3 class="text-sm font-medium text-gray-500">Exams Passed</h3>
              <p class="text-2xl font-bold text-purple-600">{{ passedExams }}</p>
            </div>
          </div>

          <!-- Filters -->
          <div class="card mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="form-label">Search Exams</label>
                <input v-model="searchQuery" type="text" placeholder="Search by exam title..." class="form-input" />
              </div>
              <div>
                <label class="form-label">Category</label>
                <select v-model="categoryFilter" class="form-input">
                  <option value="">All Categories</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="English">English</option>
                  <option value="History">History</option>
                  <option value="Computer Science">Computer Science</option>
                </select>
              </div>
              <div>
                <label class="form-label">Grade</label>
                <select v-model="gradeFilter" class="form-input">
                  <option value="">All Grades</option>
                  <option value="A">A</option>
                  <option value="B">B</option>
                  <option value="C">C</option>
                  <option value="D">D</option>
                  <option value="F">F</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Results List -->
          <div v-if="filteredResults.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p class="text-gray-500">You haven't completed any exams yet or no results match your filters.</p>
          </div>

          <div v-else class="space-y-4">
            <div v-for="result in filteredResults" :key="result.submission.id"
                 class="card hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-start">
                <!-- Exam Info -->
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-2">
                    <h3 class="text-lg font-medium text-gray-900">{{ result.exam.title }}</h3>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="getGradeBadgeClass(result.submission.grade || 'F')">
                      {{ result.submission.grade || 'Not Graded' }}
                    </span>
                  </div>

                  <p class="text-gray-600 mb-3">{{ result.exam.description }}</p>

                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Score:</span>
                      <span class="font-medium ml-1">
                        {{ result.submission.score || 0 }} / {{ result.exam.totalPoints }}
                      </span>
                    </div>
                    <div>
                      <span class="text-gray-500">Percentage:</span>
                      <span class="font-medium ml-1">{{ result.submission.percentage || 0 }}%</span>
                    </div>
                    <div>
                      <span class="text-gray-500">Time Spent:</span>
                      <span class="font-medium ml-1">{{ formatTime(result.submission.timeSpent) }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500">Submitted:</span>
                      <span class="font-medium ml-1">{{ formatDate(result.submission.submittedAt) }}</span>
                    </div>
                  </div>

                  <!-- Feedback -->
                  <div v-if="result.submission.feedback" class="mt-3 p-3 bg-blue-50 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-1">Teacher Feedback:</h4>
                    <p class="text-sm text-blue-800">{{ result.submission.feedback }}</p>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex flex-col space-y-2 ml-4">
                  <button @click="viewDetails(result)" class="btn-secondary text-sm">
                    View Details
                  </button>
                  <button v-if="result.exam.allowReview && result.submission.isGraded"
                          @click="reviewAnswers(result)" class="btn-secondary text-sm">
                    Review Answers
                  </button>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mt-4">
                <div class="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Performance</span>
                  <span>{{ result.submission.percentage || 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="h-2 rounded-full transition-all duration-300"
                       :class="getPerformanceBarClass(result.submission.percentage || 0)"
                       :style="{ width: (result.submission.percentage || 0) + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Details Modal -->
    <div v-if="showDetailsModal && selectedResult" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              {{ selectedResult.exam.title }} - Detailed Results
            </h3>
            <button @click="closeDetailsModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Questions and Answers -->
            <div class="lg:col-span-2">
              <h4 class="text-lg font-medium text-gray-900 mb-4">Your Answers</h4>
              <div class="max-h-96 overflow-y-auto space-y-4">
                <div v-for="(question, index) in selectedResult.exam.questions" :key="question.id"
                     class="border border-gray-200 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <h5 class="font-medium">Question {{ index + 1 }}</h5>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-gray-500">{{ question.points }} pts</span>
                      <span v-if="getQuestionScore(question, selectedResult.submission)"
                            class="text-sm font-medium text-green-600">
                        ✓ {{ getQuestionScore(question, selectedResult.submission) }}
                      </span>
                    </div>
                  </div>

                  <p class="text-gray-700 mb-3">{{ question.question }}</p>

                  <!-- Your Answer -->
                  <div class="mb-2 p-2 bg-blue-50 rounded">
                    <p class="text-sm text-blue-800">
                      <strong>Your Answer:</strong> {{ getStudentAnswerText(question, selectedResult.submission) }}
                    </p>
                  </div>

                  <!-- Correct Answer (if allowed) -->
                  <div v-if="selectedResult.exam.showCorrectAnswers && (question.type === 'multiple-choice' || question.type === 'true-false')"
                       class="p-2 bg-green-50 rounded">
                    <p class="text-sm text-green-800">
                      <strong>Correct Answer:</strong> {{ getCorrectAnswerText(question) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Summary -->
            <div class="lg:col-span-1">
              <div class="card">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Summary</h4>

                <div class="space-y-3 mb-6">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Questions:</span>
                    <span class="font-medium">{{ selectedResult.exam.questions.length }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Answered:</span>
                    <span class="font-medium">{{ selectedResult.submission.answers.length }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Score:</span>
                    <span class="font-medium">{{ selectedResult.submission.score }} / {{ selectedResult.exam.totalPoints }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Percentage:</span>
                    <span class="font-medium">{{ selectedResult.submission.percentage }}%</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Grade:</span>
                    <span class="font-medium">{{ selectedResult.submission.grade }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Time Spent:</span>
                    <span class="font-medium">{{ formatTime(selectedResult.submission.timeSpent) }}</span>
                  </div>
                </div>

                <button @click="closeDetailsModal" class="btn-secondary w-full">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'
import type { Exam, ExamSubmission, Question } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()

// State
const isLoading = ref(true)
const searchQuery = ref('')
const categoryFilter = ref('')
const gradeFilter = ref('')
const showDetailsModal = ref(false)
const selectedResult = ref<{ exam: Exam; submission: ExamSubmission } | null>(null)

// Computed properties
const mySubmissions = computed(() =>
  authStore.user ? submissionStore.submissionsByStudent(authStore.user.id) : []
)

const completedSubmissions = computed(() =>
  mySubmissions.value.filter(s => s.isCompleted)
)

const gradedSubmissions = computed(() =>
  completedSubmissions.value.filter(s => s.isGraded)
)

const averageScore = computed(() => {
  const graded = gradedSubmissions.value
  if (graded.length === 0) return 0
  const total = graded.reduce((sum, s) => sum + (s.percentage || 0), 0)
  return Math.round(total / graded.length)
})

const highestScore = computed(() => {
  const graded = gradedSubmissions.value
  if (graded.length === 0) return 0
  return Math.max(...graded.map(s => s.percentage || 0))
})

const passedExams = computed(() => {
  return gradedSubmissions.value.filter(s => {
    const exam = examStore.getExamById(s.examId)
    return exam && (s.percentage || 0) >= exam.passingScore
  }).length
})

const examResults = computed(() => {
  return completedSubmissions.value.map(submission => {
    const exam = examStore.getExamById(submission.examId)
    return exam ? { exam, submission } : null
  }).filter(result => result !== null) as { exam: Exam; submission: ExamSubmission }[]
})

const filteredResults = computed(() => {
  let filtered = examResults.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(result =>
      result.exam.title.toLowerCase().includes(query) ||
      result.exam.description.toLowerCase().includes(query)
    )
  }

  // Category filter
  if (categoryFilter.value) {
    filtered = filtered.filter(result => result.exam.category === categoryFilter.value)
  }

  // Grade filter
  if (gradeFilter.value) {
    filtered = filtered.filter(result => result.submission.grade === gradeFilter.value)
  }

  return filtered.sort((a, b) =>
    new Date(b.submission.submittedAt || 0).getTime() - new Date(a.submission.submittedAt || 0).getTime()
  )
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const formatDate = (date?: Date): string => {
  if (!date) return 'Not submitted'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date))
}

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const getGradeBadgeClass = (grade: string): string => {
  switch (grade) {
    case 'A':
      return 'bg-green-100 text-green-800'
    case 'B':
      return 'bg-blue-100 text-blue-800'
    case 'C':
      return 'bg-yellow-100 text-yellow-800'
    case 'D':
      return 'bg-orange-100 text-orange-800'
    case 'F':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getPerformanceBarClass = (percentage: number): string => {
  if (percentage >= 90) return 'bg-green-500'
  if (percentage >= 80) return 'bg-blue-500'
  if (percentage >= 70) return 'bg-yellow-500'
  if (percentage >= 60) return 'bg-orange-500'
  return 'bg-red-500'
}

const viewDetails = (result: { exam: Exam; submission: ExamSubmission }) => {
  selectedResult.value = result
  showDetailsModal.value = true
}

const reviewAnswers = (result: { exam: Exam; submission: ExamSubmission }) => {
  viewDetails(result)
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedResult.value = null
}

const getStudentAnswerText = (question: Question, submission: ExamSubmission): string => {
  const answer = submission.answers.find(a => a.questionId === question.id)
  if (!answer) return 'Not answered'

  if (question.type === 'multiple-choice' && question.options) {
    return question.options[answer.answer as number] || 'Invalid'
  } else if (question.type === 'true-false') {
    return answer.answer === 0 ? 'True' : 'False'
  }
  return answer.answer.toString()
}

const getCorrectAnswerText = (question: Question): string => {
  if (question.type === 'multiple-choice' && question.options) {
    return question.options[question.correctAnswer as number] || 'Invalid'
  } else if (question.type === 'true-false') {
    return question.correctAnswer === 0 ? 'True' : 'False'
  }
  return 'N/A'
}

const getQuestionScore = (question: Question, submission: ExamSubmission): number => {
  const answer = submission.answers.find(a => a.questionId === question.id)
  if (!answer) return 0

  if (question.type === 'multiple-choice' || question.type === 'true-false') {
    return answer.answer === question.correctAnswer ? question.points : 0
  }

  // For manually graded questions, we'd need to store individual question scores
  // For now, return 0 as this would require more complex grading data structure
  return 0
}

const loadData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      examStore.loadExams(),
      submissionStore.loadSubmissions()
    ])
  } catch (error) {
    console.error('Error loading data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await loadData()
})
</script>