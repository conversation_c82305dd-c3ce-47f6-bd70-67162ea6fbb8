<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>am <PERSON> -->
    <nav class="bg-white shadow sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">{{ exam?.title || 'Loading...' }}</h1>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Timer -->
            <div v-if="exam?.settings.showTimer && timeRemaining > 0"
                 class="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-lg">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span class="text-sm font-medium text-blue-800">
                {{ formatTime(timeRemaining) }}
              </span>
            </div>

            <!-- Progress -->
            <div class="text-sm text-gray-600">
              Question {{ currentQuestionIndex + 1 }} of {{ exam?.questions.length || 0 }}
            </div>

            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Exam Content -->
    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- Exam Not Found -->
      <div v-else-if="!exam" class="text-center py-12">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Exam Not Found</h3>
        <p class="text-gray-600 mb-4">The exam you're looking for doesn't exist or is no longer available.</p>
        <router-link to="/student" class="btn-primary">Back to Dashboard</router-link>
      </div>

      <!-- Exam Instructions (Before Starting) -->
      <div v-else-if="!examStarted" class="px-4 py-6 sm:px-0">
        <div class="card max-w-2xl mx-auto">
          <div class="text-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ exam.title }}</h2>
            <p class="text-gray-600">{{ exam.description }}</p>
          </div>

          <!-- Exam Details -->
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Duration</p>
              <p class="text-lg font-semibold">{{ exam.duration }} minutes</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Questions</p>
              <p class="text-lg font-semibold">{{ exam.questions.length }}</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Total Points</p>
              <p class="text-lg font-semibold">{{ exam.totalPoints }}</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Passing Score</p>
              <p class="text-lg font-semibold">{{ exam.passingScore }}%</p>
            </div>
          </div>

          <!-- Instructions -->
          <div v-if="exam.instructions" class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Instructions</h3>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p class="text-blue-800">{{ exam.instructions }}</p>
            </div>
          </div>

          <!-- General Instructions -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">General Guidelines</h3>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• Read each question carefully before answering</li>
              <li>• You can navigate between questions using the navigation buttons</li>
              <li>• Your progress is automatically saved</li>
              <li v-if="exam.settings.timeLimit">• The exam has a time limit of {{ exam.duration }} minutes</li>
              <li>• Make sure to submit your exam before the time runs out</li>
              <li v-if="exam.allowReview">• You can review your answers before submitting</li>
            </ul>
          </div>

          <!-- Start Button -->
          <div class="text-center">
            <button @click="startExam" :disabled="submissionStore.isLoading" class="btn-primary text-lg px-8 py-3">
              {{ submissionStore.isLoading ? 'Starting...' : 'Start Exam' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Exam Questions -->
      <div v-else-if="!examCompleted" class="px-4 py-6 sm:px-0">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- Question Content -->
          <div class="lg:col-span-3">
            <div class="card">
              <!-- Progress Bar -->
              <div class="mb-6">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{{ Math.round(((currentQuestionIndex + 1) / exam.questions.length) * 100) }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                       :style="{ width: ((currentQuestionIndex + 1) / exam.questions.length) * 100 + '%' }"></div>
                </div>
              </div>

              <!-- Current Question -->
              <div v-if="currentQuestion">
                <div class="flex justify-between items-start mb-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    Question {{ currentQuestionIndex + 1 }}
                  </h3>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">{{ currentQuestion.points }} points</span>
                    <button @click="toggleFlag"
                            :class="isQuestionFlagged ? 'text-red-600' : 'text-gray-400'"
                            class="hover:text-red-600">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div class="mb-6">
                  <p class="text-gray-900 whitespace-pre-wrap">{{ currentQuestion.question }}</p>
                </div>

                <!-- Answer Options -->
                <div class="space-y-4">
                  <!-- Multiple Choice -->
                  <div v-if="currentQuestion.type === 'multiple-choice'" class="space-y-3">
                    <div v-for="(option, index) in currentQuestion.options" :key="index"
                         class="flex items-center">
                      <input :id="`option-${index}`"
                             :value="index"
                             v-model="currentAnswer"
                             type="radio"
                             :name="`question-${currentQuestion.id}`"
                             class="h-4 w-4 text-blue-600" />
                      <label :for="`option-${index}`" class="ml-3 text-gray-900 cursor-pointer">
                        {{ option }}
                      </label>
                    </div>
                  </div>

                  <!-- True/False -->
                  <div v-else-if="currentQuestion.type === 'true-false'" class="space-y-3">
                    <div class="flex items-center">
                      <input id="true" :value="0" v-model="currentAnswer" type="radio"
                             :name="`question-${currentQuestion.id}`" class="h-4 w-4 text-blue-600" />
                      <label for="true" class="ml-3 text-gray-900 cursor-pointer">True</label>
                    </div>
                    <div class="flex items-center">
                      <input id="false" :value="1" v-model="currentAnswer" type="radio"
                             :name="`question-${currentQuestion.id}`" class="h-4 w-4 text-blue-600" />
                      <label for="false" class="ml-3 text-gray-900 cursor-pointer">False</label>
                    </div>
                  </div>

                  <!-- Short Answer -->
                  <div v-else-if="currentQuestion.type === 'short-answer'">
                    <textarea v-model="currentAnswer" rows="3" class="form-input"
                              placeholder="Enter your answer..."></textarea>
                  </div>

                  <!-- Essay -->
                  <div v-else-if="currentQuestion.type === 'essay'">
                    <textarea v-model="currentAnswer" rows="8" class="form-input"
                              placeholder="Write your essay response..."></textarea>
                  </div>
                </div>
              </div>

              <!-- Navigation -->
              <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                <button @click="previousQuestion"
                        :disabled="currentQuestionIndex === 0"
                        class="btn-secondary">
                  ← Previous
                </button>

                <div class="text-sm text-gray-500">
                  {{ answeredQuestions.length }} of {{ exam.questions.length }} answered
                </div>

                <button v-if="currentQuestionIndex < exam.questions.length - 1"
                        @click="nextQuestion"
                        class="btn-primary">
                  Next →
                </button>
                <button v-else @click="showReviewModal = true" class="btn-primary">
                  Review & Submit
                </button>
              </div>
            </div>
          </div>

          <!-- Question Navigation Sidebar -->
          <div class="lg:col-span-1">
            <div class="card sticky top-24">
              <h4 class="text-lg font-medium text-gray-900 mb-4">Question Navigation</h4>

              <div class="grid grid-cols-5 lg:grid-cols-4 gap-2">
                <button v-for="(question, index) in exam.questions" :key="question.id"
                        @click="goToQuestion(index)"
                        :class="getQuestionButtonClass(index)"
                        class="w-8 h-8 text-sm font-medium rounded transition-colors">
                  {{ index + 1 }}
                </button>
              </div>

              <!-- Legend -->
              <div class="mt-4 space-y-2 text-xs">
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-blue-600 rounded mr-2"></div>
                  <span>Current</span>
                </div>
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-green-600 rounded mr-2"></div>
                  <span>Answered</span>
                </div>
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-red-600 rounded mr-2"></div>
                  <span>Flagged</span>
                </div>
                <div class="flex items-center">
                  <div class="w-3 h-3 bg-gray-300 rounded mr-2"></div>
                  <span>Not answered</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Exam Completed -->
      <div v-else class="px-4 py-6 sm:px-0">
        <div class="card max-w-2xl mx-auto text-center">
          <div class="mb-6">
            <svg class="mx-auto h-16 w-16 text-green-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Exam Submitted Successfully!</h2>
            <p class="text-gray-600">Your answers have been recorded and will be graded soon.</p>
          </div>

          <div v-if="submissionResult" class="bg-gray-50 rounded-lg p-6 mb-6">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p class="text-gray-600">Questions Answered</p>
                <p class="text-lg font-semibold">{{ submissionResult.answers.length }} / {{ exam?.questions.length }}</p>
              </div>
              <div>
                <p class="text-gray-600">Time Spent</p>
                <p class="text-lg font-semibold">{{ formatTime(submissionResult.timeSpent) }}</p>
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <router-link to="/student/results" class="btn-primary block">
              View Results
            </router-link>
            <router-link to="/student" class="btn-secondary block">
              Back to Dashboard
            </router-link>
          </div>
        </div>
      </div>
    </main>

    <!-- Review Modal -->
    <div v-if="showReviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Review Your Answers</h3>

          <div class="max-h-96 overflow-y-auto mb-6">
            <div class="space-y-4">
              <div v-for="(question, index) in exam?.questions" :key="question.id"
                   class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-2">
                  <h4 class="font-medium">Question {{ index + 1 }}</h4>
                  <span v-if="!getAnswerForQuestion(question.id)"
                        class="text-red-600 text-sm">Not answered</span>
                </div>
                <p class="text-gray-700 mb-2">{{ question.question }}</p>
                <div class="text-sm">
                  <span class="text-gray-600">Your answer: </span>
                  <span class="font-medium">
                    {{ formatAnswer(question, getAnswerForQuestion(question.id)) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
              <div>
                <h4 class="text-yellow-800 font-medium">Before you submit:</h4>
                <ul class="text-yellow-700 text-sm mt-1">
                  <li>• Make sure you've answered all questions</li>
                  <li>• Review your answers carefully</li>
                  <li>• Once submitted, you cannot change your answers</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button @click="showReviewModal = false" class="btn-secondary">
              Continue Editing
            </button>
            <button @click="submitExam" :disabled="submissionStore.isLoading" class="btn-primary">
              {{ submissionStore.isLoading ? 'Submitting...' : 'Submit Exam' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Time Warning Modal -->
    <div v-if="showTimeWarning" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <svg class="mx-auto h-12 w-12 text-red-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Time Warning</h3>
          <p class="text-sm text-gray-500 mb-6">
            You have {{ formatTime(timeRemaining) }} remaining. Please complete your exam soon.
          </p>
          <button @click="showTimeWarning = false" class="btn-primary">
            Continue
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'
import type { Exam, Question, Answer, ExamSubmission } from '@/types'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()

// State
const isLoading = ref(true)
const exam = ref<Exam | null>(null)
const examStarted = ref(false)
const examCompleted = ref(false)
const currentQuestionIndex = ref(0)
const answers = ref<Map<string, any>>(new Map())
const flaggedQuestions = ref<Set<string>>(new Set())
const timeRemaining = ref(0)
const showReviewModal = ref(false)
const showTimeWarning = ref(false)
const submissionResult = ref<ExamSubmission | null>(null)

// Timer
let timer: NodeJS.Timeout | null = null

// Computed properties
const currentQuestion = computed(() =>
  exam.value?.questions[currentQuestionIndex.value] || null
)

const currentAnswer = computed({
  get: () => answers.value.get(currentQuestion.value?.id || ''),
  set: (value) => {
    if (currentQuestion.value) {
      answers.value.set(currentQuestion.value.id, value)
      saveAnswer(currentQuestion.value.id, value)
    }
  }
})

const answeredQuestions = computed(() =>
  exam.value?.questions.filter(q => answers.value.has(q.id)) || []
)

const isQuestionFlagged = computed(() =>
  currentQuestion.value ? flaggedQuestions.value.has(currentQuestion.value.id) : false
)

// Methods
const logout = () => {
  if (examStarted.value && !examCompleted.value) {
    if (confirm('Are you sure you want to logout? Your progress will be saved.')) {
      authStore.logout()
      router.push('/login')
    }
  } else {
    authStore.logout()
    router.push('/login')
  }
}

const loadExam = async () => {
  isLoading.value = true
  try {
    const examId = route.params.examId as string
    await examStore.loadExams()

    exam.value = examStore.getExamById(examId) || null

    if (!exam.value) {
      console.error('Exam not found')
      return
    }

    // Check if student already has a submission in progress
    await submissionStore.loadSubmissions()
    const existingSubmission = submissionStore.submissions.find(
      s => s.examId === examId &&
           s.studentId === authStore.user?.id &&
           s.status === 'in-progress'
    )

    if (existingSubmission) {
      // Resume existing exam
      examStarted.value = true
      loadExistingAnswers(existingSubmission)
      startTimer(existingSubmission.timeSpent)
    }
  } catch (error) {
    console.error('Error loading exam:', error)
  } finally {
    isLoading.value = false
  }
}

const loadExistingAnswers = (submission: ExamSubmission) => {
  answers.value.clear()
  submission.answers.forEach(answer => {
    answers.value.set(answer.questionId, answer.answer)
    if (answer.flagged) {
      flaggedQuestions.value.add(answer.questionId)
    }
  })
}

const startExam = async () => {
  if (!exam.value || !authStore.user) return

  const result = await submissionStore.startExam(exam.value.id, authStore.user.id)
  if (result.success) {
    examStarted.value = true
    startTimer(0)
  } else {
    alert('Failed to start exam: ' + result.message)
  }
}

const startTimer = (initialTimeSpent: number = 0) => {
  if (!exam.value?.settings.timeLimit) return

  timeRemaining.value = (exam.value.duration * 60) - initialTimeSpent

  timer = setInterval(() => {
    timeRemaining.value--

    // Show warning at 5 minutes
    if (timeRemaining.value === 300 && !showTimeWarning.value) {
      showTimeWarning.value = true
    }

    // Auto-submit when time runs out
    if (timeRemaining.value <= 0) {
      autoSubmitExam()
    }
  }, 1000)
}

const saveAnswer = async (questionId: string, answer: any) => {
  if (!examStarted.value) return
  await submissionStore.saveAnswer(questionId, answer)
}

const toggleFlag = () => {
  if (!currentQuestion.value) return

  if (flaggedQuestions.value.has(currentQuestion.value.id)) {
    flaggedQuestions.value.delete(currentQuestion.value.id)
  } else {
    flaggedQuestions.value.add(currentQuestion.value.id)
  }
}

const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

const nextQuestion = () => {
  if (exam.value && currentQuestionIndex.value < exam.value.questions.length - 1) {
    currentQuestionIndex.value++
  }
}

const goToQuestion = (index: number) => {
  currentQuestionIndex.value = index
}

const getQuestionButtonClass = (index: number) => {
  const question = exam.value?.questions[index]
  if (!question) return 'bg-gray-300 text-gray-600'

  const isAnswered = answers.value.has(question.id)
  const isFlagged = flaggedQuestions.value.has(question.id)
  const isCurrent = index === currentQuestionIndex.value

  if (isCurrent) return 'bg-blue-600 text-white'
  if (isFlagged) return 'bg-red-600 text-white'
  if (isAnswered) return 'bg-green-600 text-white'
  return 'bg-gray-300 text-gray-600 hover:bg-gray-400'
}

const getAnswerForQuestion = (questionId: string) => {
  return answers.value.get(questionId)
}

const formatAnswer = (question: Question, answer: any) => {
  if (answer === undefined || answer === null || answer === '') {
    return 'Not answered'
  }

  switch (question.type) {
    case 'multiple-choice':
      return question.options?.[answer] || 'Invalid option'
    case 'true-false':
      return answer === 0 ? 'True' : 'False'
    case 'short-answer':
    case 'essay':
      return answer.toString().substring(0, 100) + (answer.length > 100 ? '...' : '')
    default:
      return answer.toString()
  }
}

const submitExam = async () => {
  showReviewModal.value = false

  const result = await submissionStore.submitExam()
  if (result.success) {
    examCompleted.value = true
    submissionResult.value = result.data || null
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  } else {
    alert('Failed to submit exam: ' + result.message)
  }
}

const autoSubmitExam = async () => {
  if (examCompleted.value) return

  alert('Time is up! Your exam will be submitted automatically.')
  await submitExam()
}

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Lifecycle
onMounted(async () => {
  await loadExam()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// Prevent page refresh during exam
const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  if (examStarted.value && !examCompleted.value) {
    e.preventDefault()
    e.returnValue = ''
  }
}

onMounted(() => {
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>