<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">Student Dashboard</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">Welcome, {{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Stats Cards -->
          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">Completed Exams</h3>
            <p class="text-3xl font-bold text-green-600">{{ stats.completedExams }}</p>
          </div>

          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">Pending Exams</h3>
            <p class="text-3xl font-bold text-orange-600">{{ stats.pendingExams }}</p>
          </div>

          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">Average Score</h3>
            <p class="text-3xl font-bold text-blue-600">{{ stats.averageScore }}%</p>
          </div>
        </div>

        <!-- Available Exams -->
        <div class="mt-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Available Exams</h2>
          <div class="space-y-4">
            <div class="card">
              <h3 class="text-lg font-medium text-gray-900">Mathematics Quiz #1</h3>
              <p class="text-gray-600">Duration: 30 minutes | Questions: 20</p>
              <button class="btn-primary mt-2">Start Exam</button>
            </div>

            <div class="card">
              <h3 class="text-lg font-medium text-gray-900">Science Test</h3>
              <p class="text-gray-600">Duration: 45 minutes | Questions: 25</p>
              <button class="btn-primary mt-2">Start Exam</button>
            </div>
          </div>
        </div>

        <!-- Recent Results -->
        <div class="mt-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Results</h2>
          <router-link to="/student/results" class="card hover:shadow-lg transition-shadow cursor-pointer block">
            <h3 class="text-lg font-medium text-gray-900">View All Results</h3>
            <p class="text-gray-600">Check your exam scores and feedback</p>
          </router-link>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const stats = ref({
  completedExams: 0,
  pendingExams: 0,
  averageScore: 0
})

const logout = () => {
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  // Mock data
  stats.value = {
    completedExams: 5,
    pendingExams: 2,
    averageScore: 87
  }
})
</script>