<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">Student Dashboard</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">Welcome, {{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <div v-else>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Stats Cards -->
            <div class="card">
              <h3 class="text-lg font-medium text-gray-900">Completed Exams</h3>
              <p class="text-3xl font-bold text-green-600">{{ completedExams }}</p>
            </div>

            <div class="card">
              <h3 class="text-lg font-medium text-gray-900">Pending Exams</h3>
              <p class="text-3xl font-bold text-orange-600">{{ pendingExams }}</p>
            </div>

            <div class="card">
              <h3 class="text-lg font-medium text-gray-900">Average Score</h3>
              <p class="text-3xl font-bold text-blue-600">{{ averageScore }}%</p>
            </div>
          </div>

          <!-- Available Exams -->
          <div class="mt-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-medium text-gray-900">Available Exams</h2>
              <router-link to="/student/exams" class="text-blue-600 hover:text-blue-800 text-sm">
                View All
              </router-link>
            </div>

            <div v-if="availableExams.length === 0" class="text-center py-8 text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <p>No exams available at the moment.</p>
            </div>

            <div v-else class="space-y-4">
              <div v-for="exam in availableExams" :key="exam.id" class="card">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900 mb-1">{{ exam.title }}</h3>
                    <p class="text-gray-600 mb-2">{{ exam.description }}</p>
                    <div class="flex space-x-4 text-sm text-gray-500">
                      <span>Duration: {{ exam.duration }} minutes</span>
                      <span>Questions: {{ exam.questions.length }}</span>
                      <span>Points: {{ exam.totalPoints }}</span>
                    </div>
                  </div>
                  <button @click="startExam(exam.id)" class="btn-primary ml-4">
                    Start Exam
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Results -->
          <div class="mt-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-medium text-gray-900">Recent Results</h2>
              <router-link to="/student/results" class="text-blue-600 hover:text-blue-800 text-sm">
                View All Results
              </router-link>
            </div>

            <div v-if="recentResults.length === 0" class="card text-center py-8 text-gray-500">
              <p>No graded exams yet. Complete some exams to see your results here.</p>
            </div>

            <div v-else class="space-y-4">
              <div v-for="result in recentResults" :key="result.submission.id" class="card">
                <div class="flex justify-between items-center">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">{{ result.exam.title }}</h3>
                    <p class="text-gray-600">Score: {{ result.submission.score }} / {{ result.exam.totalPoints }} ({{ result.submission.percentage }}%)</p>
                    <p class="text-sm text-gray-500">Grade: {{ result.submission.grade }}</p>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="result.submission.grade === 'A' ? 'bg-green-100 text-green-800' :
                                 result.submission.grade === 'B' ? 'bg-blue-100 text-blue-800' :
                                 result.submission.grade === 'C' ? 'bg-yellow-100 text-yellow-800' :
                                 result.submission.grade === 'D' ? 'bg-orange-100 text-orange-800' :
                                 'bg-red-100 text-red-800'">
                      {{ result.submission.grade }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'

const router = useRouter()
const authStore = useAuthStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()

const isLoading = ref(true)

// Computed properties
const mySubmissions = computed(() =>
  authStore.user ? submissionStore.submissionsByStudent(authStore.user.id) : []
)

const completedExams = computed(() =>
  mySubmissions.value.filter(s => s.isCompleted).length
)

const pendingExams = computed(() =>
  examStore.activeExams.filter(exam =>
    !mySubmissions.value.some(s => s.examId === exam.id && s.isCompleted)
  ).length
)

const averageScore = computed(() => {
  const graded = mySubmissions.value.filter(s => s.isGraded)
  if (graded.length === 0) return 0
  const total = graded.reduce((sum, s) => sum + (s.percentage || 0), 0)
  return Math.round(total / graded.length)
})

const availableExams = computed(() =>
  examStore.activeExams.filter(exam =>
    !mySubmissions.value.some(s => s.examId === exam.id && s.isCompleted)
  ).slice(0, 3) // Show only first 3
)

const recentResults = computed(() =>
  mySubmissions.value
    .filter(s => s.isGraded)
    .sort((a, b) => new Date(b.gradedAt || 0).getTime() - new Date(a.gradedAt || 0).getTime())
    .slice(0, 3)
    .map(submission => {
      const exam = examStore.getExamById(submission.examId)
      return exam ? { exam, submission } : null
    })
    .filter(result => result !== null)
)

const logout = () => {
  authStore.logout()
  router.push('/login')
}

const startExam = (examId: string) => {
  router.push(`/student/exam/${examId}`)
}

const loadData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      examStore.loadExams(),
      submissionStore.loadSubmissions()
    ])
  } catch (error) {
    console.error('Error loading data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await loadData()
})
</script>