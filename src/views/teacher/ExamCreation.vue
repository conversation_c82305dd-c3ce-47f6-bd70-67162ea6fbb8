<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/teacher" class="text-blue-600 hover:text-blue-800 mr-4">← Back</router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              {{ isEditMode ? 'Edit Exam' : 'Create Exam' }}
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <button @click="saveAsDraft" :disabled="examStore.isLoading" class="btn-secondary">
              Save as Draft
            </button>
            <button @click="publishExam" :disabled="examStore.isLoading || !canPublish" class="btn-primary">
              Publish Exam
            </button>
            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Main Content -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Exam Details -->
            <div class="card">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Exam Details</h3>

              <div class="space-y-4">
                <div>
                  <label class="form-label">Exam Title</label>
                  <input v-model="examForm.title" type="text" required class="form-input"
                         placeholder="Enter exam title..." />
                </div>

                <div>
                  <label class="form-label">Description</label>
                  <textarea v-model="examForm.description" rows="3" class="form-input"
                            placeholder="Describe what this exam covers..."></textarea>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="form-label">Category</label>
                    <select v-model="examForm.category" required class="form-input">
                      <option value="">Select Category</option>
                      <option value="Mathematics">Mathematics</option>
                      <option value="Science">Science</option>
                      <option value="English">English</option>
                      <option value="History">History</option>
                      <option value="Computer Science">Computer Science</option>
                    </select>
                  </div>

                  <div>
                    <label class="form-label">Duration (minutes)</label>
                    <input v-model.number="examForm.duration" type="number" min="1" required class="form-input" />
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="form-label">Passing Score (%)</label>
                    <input v-model.number="examForm.passingScore" type="number" min="0" max="100" required class="form-input" />
                  </div>

                  <div>
                    <label class="form-label">Max Attempts</label>
                    <input v-model.number="examForm.maxAttempts" type="number" min="1" required class="form-input" />
                  </div>
                </div>

                <div>
                  <label class="form-label">Instructions</label>
                  <textarea v-model="examForm.instructions" rows="3" class="form-input"
                            placeholder="Special instructions for students..."></textarea>
                </div>
              </div>
            </div>

            <!-- Questions Section -->
            <div class="card">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Questions</h3>
                <div class="space-x-2">
                  <button @click="addQuestion('multiple-choice')" class="btn-secondary text-sm">
                    + Multiple Choice
                  </button>
                  <button @click="addQuestion('true-false')" class="btn-secondary text-sm">
                    + True/False
                  </button>
                  <button @click="addQuestion('short-answer')" class="btn-secondary text-sm">
                    + Short Answer
                  </button>
                  <button @click="addQuestion('essay')" class="btn-secondary text-sm">
                    + Essay
                  </button>
                </div>
              </div>

              <!-- Questions List -->
              <div v-if="examForm.questions.length === 0" class="text-center py-8 text-gray-500">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <p>No questions added yet. Click the buttons above to add questions.</p>
              </div>

              <div v-else class="space-y-4">
                <div v-for="(question, index) in examForm.questions" :key="question.id"
                     class="border border-gray-200 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-500">Question {{ index + 1 }}</span>
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {{ formatQuestionType(question.type) }}
                      </span>
                    </div>
                    <div class="flex space-x-2">
                      <button @click="editQuestion(index)" class="text-blue-600 hover:text-blue-800 text-sm">
                        Edit
                      </button>
                      <button @click="deleteQuestion(index)" class="text-red-600 hover:text-red-800 text-sm">
                        Delete
                      </button>
                    </div>
                  </div>

                  <div class="space-y-3">
                    <div>
                      <label class="form-label">Question Text</label>
                      <textarea v-model="question.question" rows="2" class="form-input" required></textarea>
                    </div>

                    <!-- Multiple Choice Options -->
                    <div v-if="question.type === 'multiple-choice'">
                      <label class="form-label">Options</label>
                      <div class="space-y-2">
                        <div v-for="(option, optionIndex) in question.options" :key="optionIndex"
                             class="flex items-center space-x-2">
                          <input type="radio" :name="`correct-${question.id}`"
                                 :checked="question.correctAnswer === optionIndex"
                                 @change="question.correctAnswer = optionIndex" />
                          <input v-model="question.options[optionIndex]" type="text"
                                 class="form-input flex-1" placeholder="Enter option..." />
                          <button @click="removeOption(question, optionIndex)"
                                  class="text-red-600 hover:text-red-800">
                            Remove
                          </button>
                        </div>
                        <button @click="addOption(question)" class="btn-secondary text-sm">
                          Add Option
                        </button>
                      </div>
                    </div>

                    <!-- True/False -->
                    <div v-if="question.type === 'true-false'">
                      <label class="form-label">Correct Answer</label>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input type="radio" :name="`tf-${question.id}`" :value="0"
                                 v-model="question.correctAnswer" />
                          <span class="ml-2">True</span>
                        </label>
                        <label class="flex items-center">
                          <input type="radio" :name="`tf-${question.id}`" :value="1"
                                 v-model="question.correctAnswer" />
                          <span class="ml-2">False</span>
                        </label>
                      </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                      <div>
                        <label class="form-label">Points</label>
                        <input v-model.number="question.points" type="number" min="1" class="form-input" />
                      </div>
                      <div>
                        <label class="form-label">Difficulty</label>
                        <select v-model="question.difficulty" class="form-input">
                          <option value="easy">Easy</option>
                          <option value="medium">Medium</option>
                          <option value="hard">Hard</option>
                        </select>
                      </div>
                      <div class="flex items-center pt-6">
                        <input v-model="question.required" type="checkbox" class="h-4 w-4 text-blue-600" />
                        <label class="ml-2 text-sm text-gray-700">Required</label>
                      </div>
                    </div>

                    <div v-if="question.type === 'short-answer' || question.type === 'essay'">
                      <label class="form-label">Sample Answer (for grading reference)</label>
                      <textarea v-model="question.explanation" rows="2" class="form-input"
                                placeholder="Provide a sample answer or grading notes..."></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Exam Settings -->
            <div class="card">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Exam Settings</h3>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Time Limit</span>
                  <input v-model="examForm.settings.timeLimit" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Show Timer</span>
                  <input v-model="examForm.settings.showTimer" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Randomize Questions</span>
                  <input v-model="examForm.settings.shuffleQuestions" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Show Results</span>
                  <input v-model="examForm.showResults" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Show Correct Answers</span>
                  <input v-model="examForm.showCorrectAnswers" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">Allow Review</span>
                  <input v-model="examForm.allowReview" type="checkbox" class="h-4 w-4 text-blue-600" />
                </div>
              </div>
            </div>

            <!-- Exam Summary -->
            <div class="card">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Exam Summary</h3>

              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Total Questions:</span>
                  <span class="font-medium">{{ examForm.questions.length }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Total Points:</span>
                  <span class="font-medium">{{ totalPoints }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Duration:</span>
                  <span class="font-medium">{{ examForm.duration }} minutes</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Passing Score:</span>
                  <span class="font-medium">{{ examForm.passingScore }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import type { Question, Exam } from '@/types'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const examStore = useExamStore()

// Check if we're in edit mode
const isEditMode = computed(() => !!route.params.examId)
const editingExamId = computed(() => route.params.examId as string)

// Form data
const examForm = ref({
  title: '',
  description: '',
  category: '',
  duration: 60,
  passingScore: 70,
  maxAttempts: 3,
  instructions: '',
  questions: [] as Question[],
  showResults: true,
  showCorrectAnswers: true,
  allowReview: true,
  settings: {
    timeLimit: true,
    showTimer: true,
    preventCheating: false,
    fullScreen: false,
    disableCopy: false,
    shuffleQuestions: false
  }
})

// Computed properties
const totalPoints = computed(() =>
  examForm.value.questions.reduce((sum, q) => sum + q.points, 0)
)

const canPublish = computed(() =>
  examForm.value.title &&
  examForm.value.category &&
  examForm.value.questions.length > 0 &&
  examForm.value.questions.every(q => q.question.trim() !== '')
)

// Methods
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const addQuestion = (type: Question['type']) => {
  const newQuestion: Question = {
    id: Date.now().toString(),
    type,
    question: '',
    points: 10,
    difficulty: 'medium',
    tags: [],
    required: true,
    order: examForm.value.questions.length + 1
  }

  if (type === 'multiple-choice') {
    newQuestion.options = ['', '', '', '']
    newQuestion.correctAnswer = 0
  } else if (type === 'true-false') {
    newQuestion.correctAnswer = 0
  }

  examForm.value.questions.push(newQuestion)
}

const editQuestion = (index: number) => {
  // For now, questions are edited inline
  // Could implement a modal for more complex editing
  console.log('Editing question', index)
}

const deleteQuestion = (index: number) => {
  examForm.value.questions.splice(index, 1)
  // Update order numbers
  examForm.value.questions.forEach((q, i) => {
    q.order = i + 1
  })
}

const addOption = (question: Question) => {
  if (!question.options) question.options = []
  question.options.push('')
}

const removeOption = (question: Question, optionIndex: number) => {
  if (!question.options || question.options.length <= 2) return
  question.options.splice(optionIndex, 1)

  // Adjust correct answer if needed
  if (question.correctAnswer === optionIndex) {
    question.correctAnswer = 0
  } else if (question.correctAnswer > optionIndex) {
    question.correctAnswer = (question.correctAnswer as number) - 1
  }
}

const formatQuestionType = (type: string) => {
  switch (type) {
    case 'multiple-choice':
      return 'Multiple Choice'
    case 'true-false':
      return 'True/False'
    case 'short-answer':
      return 'Short Answer'
    case 'essay':
      return 'Essay'
    default:
      return type
  }
}

const saveAsDraft = async () => {
  if (!examForm.value.title) {
    alert('Please enter an exam title')
    return
  }

  const examData = {
    ...examForm.value,
    teacherId: authStore.user?.id || '',
    totalPoints: totalPoints.value,
    isActive: false,
    isPublished: false,
    randomizeQuestions: examForm.value.settings.shuffleQuestions,
    randomizeOptions: false,
    tags: [examForm.value.category.toLowerCase()]
  }

  let result
  if (isEditMode.value) {
    result = await examStore.updateExam(editingExamId.value, examData)
  } else {
    result = await examStore.createExam(examData)
  }

  if (result.success) {
    alert(`Exam ${isEditMode.value ? 'updated' : 'saved'} as draft successfully!`)
    router.push('/teacher/exams')
  } else {
    alert(`Failed to ${isEditMode.value ? 'update' : 'save'} exam: ` + result.message)
  }
}

const publishExam = async () => {
  if (!canPublish.value) {
    alert('Please complete all required fields and add at least one question')
    return
  }

  const examData = {
    ...examForm.value,
    teacherId: authStore.user?.id || '',
    totalPoints: totalPoints.value,
    isActive: true,
    isPublished: true,
    randomizeQuestions: examForm.value.settings.shuffleQuestions,
    randomizeOptions: false,
    tags: [examForm.value.category.toLowerCase()]
  }

  let result
  if (isEditMode.value) {
    result = await examStore.updateExam(editingExamId.value, examData)
  } else {
    result = await examStore.createExam(examData)
  }

  if (result.success) {
    alert(`Exam ${isEditMode.value ? 'updated and published' : 'published'} successfully!`)
    router.push('/teacher/exams')
  } else {
    alert(`Failed to ${isEditMode.value ? 'update' : 'publish'} exam: ` + result.message)
  }
}

const loadExamForEdit = async () => {
  if (!isEditMode.value) return

  await examStore.loadExams()
  const exam = examStore.getExamById(editingExamId.value)

  if (!exam) {
    alert('Exam not found')
    router.push('/teacher/exams')
    return
  }

  // Check if current user is the owner
  if (exam.teacherId !== authStore.user?.id) {
    alert('You can only edit your own exams')
    router.push('/teacher/exams')
    return
  }

  // Load exam data into form
  examForm.value = {
    title: exam.title,
    description: exam.description,
    category: exam.category,
    duration: exam.duration,
    passingScore: exam.passingScore,
    maxAttempts: exam.maxAttempts,
    instructions: exam.instructions || '',
    questions: [...exam.questions],
    showResults: exam.showResults,
    showCorrectAnswers: exam.showCorrectAnswers,
    allowReview: exam.allowReview,
    settings: { ...exam.settings }
  }
}

onMounted(async () => {
  if (isEditMode.value) {
    await loadExamForEdit()
  }
})
</script>