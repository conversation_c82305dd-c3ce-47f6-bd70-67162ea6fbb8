<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/teacher" class="text-blue-600 hover:text-blue-800 mr-4">← Back</router-link>
            <h1 class="text-xl font-semibold text-gray-900">Exam Management</h1>
          </div>
          <div class="flex items-center space-x-4">
            <router-link to="/teacher/exams/create" class="btn-primary">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Create Exam
            </router-link>
            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Header -->
        <div class="mb-6">
          <h2 class="text-2xl font-bold text-gray-900">My Exams</h2>
          <p class="text-gray-600">Manage your exams, view submissions, and track student progress</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Total Exams</h3>
            <p class="text-2xl font-bold text-gray-900">{{ myExams.length }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Published</h3>
            <p class="text-2xl font-bold text-green-600">{{ publishedExams.length }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Draft</h3>
            <p class="text-2xl font-bold text-orange-600">{{ draftExams.length }}</p>
          </div>
          <div class="card">
            <h3 class="text-sm font-medium text-gray-500">Total Submissions</h3>
            <p class="text-2xl font-bold text-blue-600">{{ totalSubmissions }}</p>
          </div>
        </div>

        <!-- Filters -->
        <div class="card mb-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="form-label">Search Exams</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search by title or category..."
                class="form-input"
              />
            </div>
            <div>
              <label class="form-label">Status</label>
              <select v-model="statusFilter" class="form-input">
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div>
              <label class="form-label">Category</label>
              <select v-model="categoryFilter" class="form-input">
                <option value="">All Categories</option>
                <option value="Mathematics">Mathematics</option>
                <option value="Science">Science</option>
                <option value="English">English</option>
                <option value="History">History</option>
                <option value="Computer Science">Computer Science</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Exams Grid -->
        <div v-if="filteredExams.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
          <p class="text-gray-500 mb-4">Get started by creating your first exam.</p>
          <router-link to="/teacher/exams/create" class="btn-primary">
            Create Your First Exam
          </router-link>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="exam in filteredExams" :key="exam.id" class="card hover:shadow-lg transition-shadow">
            <!-- Exam Header -->
            <div class="flex justify-between items-start mb-4">
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-1">{{ exam.title }}</h3>
                <p class="text-sm text-gray-500">{{ exam.category }}</p>
              </div>
              <div class="flex space-x-1">
                <span v-if="exam.isPublished" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                  Published
                </span>
                <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                  Draft
                </span>
                <span v-if="exam.isActive" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  Active
                </span>
              </div>
            </div>

            <!-- Exam Details -->
            <div class="space-y-2 mb-4">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Questions:</span>
                <span class="font-medium">{{ exam.questions.length }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Duration:</span>
                <span class="font-medium">{{ exam.duration }} min</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Total Points:</span>
                <span class="font-medium">{{ exam.totalPoints }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Submissions:</span>
                <span class="font-medium">{{ getSubmissionCount(exam.id) }}</span>
              </div>
            </div>

            <!-- Description -->
            <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ exam.description }}</p>

            <!-- Actions -->
            <div class="flex flex-wrap gap-2">
              <button @click="editExam(exam)" class="btn-secondary text-sm">
                Edit
              </button>
              <button @click="viewSubmissions(exam)" class="btn-secondary text-sm">
                Submissions ({{ getSubmissionCount(exam.id) }})
              </button>
              <button @click="duplicateExam(exam)" class="btn-secondary text-sm">
                Duplicate
              </button>
              <button v-if="exam.isPublished" @click="unpublishExam(exam)" class="btn-secondary text-sm">
                Unpublish
              </button>
              <button v-else @click="publishExam(exam)" class="btn-primary text-sm">
                Publish
              </button>
              <button @click="deleteExam(exam)" class="text-red-600 hover:text-red-800 text-sm">
                Delete
              </button>
            </div>

            <!-- Quick Stats -->
            <div v-if="exam.isPublished" class="mt-4 pt-4 border-t border-gray-200">
              <div class="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p class="text-xs text-gray-500">Avg Score</p>
                  <p class="text-sm font-medium">{{ getAverageScore(exam.id) }}%</p>
                </div>
                <div>
                  <p class="text-xs text-gray-500">Pass Rate</p>
                  <p class="text-sm font-medium">{{ getPassRate(exam.id) }}%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Delete</h3>
          <p class="text-sm text-gray-500 mb-6">
            Are you sure you want to delete "{{ examToDelete?.title }}"? This action cannot be undone.
          </p>
          <div class="flex justify-center space-x-3">
            <button @click="showDeleteModal = false" class="btn-secondary">Cancel</button>
            <button @click="confirmDelete" :disabled="examStore.isLoading" class="btn-danger">
              {{ examStore.isLoading ? 'Deleting...' : 'Delete' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'
import type { Exam } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()

// State
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const showDeleteModal = ref(false)
const examToDelete = ref<Exam | null>(null)

// Computed properties
const myExams = computed(() =>
  examStore.examsByTeacher(authStore.user?.id || '')
)

const publishedExams = computed(() =>
  myExams.value.filter(exam => exam.isPublished)
)

const draftExams = computed(() =>
  myExams.value.filter(exam => !exam.isPublished)
)

const totalSubmissions = computed(() =>
  myExams.value.reduce((total, exam) => total + getSubmissionCount(exam.id), 0)
)

const filteredExams = computed(() => {
  let filtered = myExams.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(exam =>
      exam.title.toLowerCase().includes(query) ||
      exam.category.toLowerCase().includes(query) ||
      exam.description.toLowerCase().includes(query)
    )
  }

  // Status filter
  if (statusFilter.value) {
    switch (statusFilter.value) {
      case 'published':
        filtered = filtered.filter(exam => exam.isPublished)
        break
      case 'draft':
        filtered = filtered.filter(exam => !exam.isPublished)
        break
      case 'active':
        filtered = filtered.filter(exam => exam.isActive)
        break
      case 'inactive':
        filtered = filtered.filter(exam => !exam.isActive)
        break
    }
  }

  // Category filter
  if (categoryFilter.value) {
    filtered = filtered.filter(exam => exam.category === categoryFilter.value)
  }

  return filtered
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const getSubmissionCount = (examId: string): number => {
  return submissionStore.submissionsByExam(examId).length
}

const getAverageScore = (examId: string): number => {
  const submissions = submissionStore.submissionsByExam(examId).filter(s => s.isGraded)
  if (submissions.length === 0) return 0

  const totalScore = submissions.reduce((sum, s) => sum + (s.percentage || 0), 0)
  return Math.round(totalScore / submissions.length)
}

const getPassRate = (examId: string): number => {
  const submissions = submissionStore.submissionsByExam(examId).filter(s => s.isGraded)
  if (submissions.length === 0) return 0

  const exam = examStore.getExamById(examId)
  if (!exam) return 0

  const passedSubmissions = submissions.filter(s => (s.percentage || 0) >= exam.passingScore)
  return Math.round((passedSubmissions.length / submissions.length) * 100)
}

const editExam = (exam: Exam) => {
  // For now, redirect to create page with exam data
  // In a full implementation, you'd pass the exam data to the create page
  router.push(`/teacher/exams/edit/${exam.id}`)
}

const viewSubmissions = (exam: Exam) => {
  router.push(`/teacher/grading/${exam.id}`)
}

const duplicateExam = async (exam: Exam) => {
  const result = await examStore.duplicateExam(exam.id)
  if (result.success) {
    console.log('Exam duplicated successfully')
  } else {
    alert('Failed to duplicate exam: ' + result.message)
  }
}

const publishExam = async (exam: Exam) => {
  if (exam.questions.length === 0) {
    alert('Cannot publish exam without questions')
    return
  }

  const result = await examStore.publishExam(exam.id)
  if (result.success) {
    console.log('Exam published successfully')
  } else {
    alert('Failed to publish exam: ' + result.message)
  }
}

const unpublishExam = async (exam: Exam) => {
  const result = await examStore.unpublishExam(exam.id)
  if (result.success) {
    console.log('Exam unpublished successfully')
  } else {
    alert('Failed to unpublish exam: ' + result.message)
  }
}

const deleteExam = (exam: Exam) => {
  examToDelete.value = exam
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!examToDelete.value) return

  const result = await examStore.deleteExam(examToDelete.value.id)
  if (result.success) {
    showDeleteModal.value = false
    examToDelete.value = null
    console.log('Exam deleted successfully')
  } else {
    alert('Failed to delete exam: ' + result.message)
  }
}

onMounted(async () => {
  await Promise.all([
    examStore.loadExams(),
    submissionStore.loadSubmissions()
  ])
})
</script>