<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/teacher/exams" class="text-blue-600 hover:text-blue-800 mr-4">← Back</router-link>
            <h1 class="text-xl font-semibold text-gray-900">
              Grading: {{ exam?.title || 'Loading...' }}
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">{{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <!-- Exam Not Found -->
        <div v-else-if="!exam" class="text-center py-12">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Exam Not Found</h3>
          <p class="text-gray-600 mb-4">The exam you're looking for doesn't exist.</p>
          <router-link to="/teacher/exams" class="btn-primary">Back to Exams</router-link>
        </div>

        <!-- Grading Interface -->
        <div v-else>
          <!-- Header with Stats -->
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ exam.title }}</h2>
            <p class="text-gray-600 mb-4">{{ exam.description }}</p>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="card">
                <h3 class="text-sm font-medium text-gray-500">Total Submissions</h3>
                <p class="text-2xl font-bold text-gray-900">{{ submissions.length }}</p>
              </div>
              <div class="card">
                <h3 class="text-sm font-medium text-gray-500">Graded</h3>
                <p class="text-2xl font-bold text-green-600">{{ gradedSubmissions.length }}</p>
              </div>
              <div class="card">
                <h3 class="text-sm font-medium text-gray-500">Pending</h3>
                <p class="text-2xl font-bold text-orange-600">{{ pendingSubmissions.length }}</p>
              </div>
              <div class="card">
                <h3 class="text-sm font-medium text-gray-500">Average Score</h3>
                <p class="text-2xl font-bold text-blue-600">{{ averageScore }}%</p>
              </div>
            </div>
          </div>

          <!-- Filters -->
          <div class="card mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="form-label">Search Students</label>
                <input v-model="searchQuery" type="text" placeholder="Search by name..." class="form-input" />
              </div>
              <div>
                <label class="form-label">Status</label>
                <select v-model="statusFilter" class="form-input">
                  <option value="">All Submissions</option>
                  <option value="graded">Graded</option>
                  <option value="pending">Pending Grading</option>
                  <option value="submitted">Submitted</option>
                </select>
              </div>
              <div class="flex items-end">
                <button @click="autoGradeAll" :disabled="submissionStore.isLoading" class="btn-primary w-full">
                  Auto-Grade All
                </button>
              </div>
            </div>
          </div>

          <!-- Submissions Table -->
          <div class="card">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Score
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="submission in filteredSubmissions" :key="submission.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8">
                          <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-xs font-medium text-gray-700">
                              {{ getStudentInitials(submission.studentId) }}
                            </span>
                          </div>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">
                            {{ getStudentName(submission.studentId) }}
                          </div>
                          <div class="text-sm text-gray-500">
                            Attempt {{ submission.attemptNumber }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(submission.submittedAt) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="submission.isGraded" class="text-sm">
                        <div class="font-medium text-gray-900">
                          {{ submission.score }} / {{ exam.totalPoints }}
                        </div>
                        <div class="text-gray-500">{{ submission.percentage }}%</div>
                      </div>
                      <div v-else class="text-sm text-gray-500">Not graded</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                            :class="getStatusBadgeClass(submission.status)">
                        {{ formatStatus(submission.status) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button @click="viewSubmission(submission)" class="text-blue-600 hover:text-blue-900">
                        View
                      </button>
                      <button v-if="!submission.isGraded" @click="gradeSubmission(submission)"
                              class="text-green-600 hover:text-green-900">
                        Grade
                      </button>
                      <button v-else @click="editGrade(submission)" class="text-orange-600 hover:text-orange-900">
                        Edit Grade
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Empty State -->
            <div v-if="filteredSubmissions.length === 0" class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No submissions found</h3>
              <p class="mt-1 text-sm text-gray-500">No students have submitted this exam yet.</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Grading Modal -->
    <div v-if="showGradingModal && selectedSubmission" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              Grade Submission - {{ getStudentName(selectedSubmission.studentId) }}
            </h3>
            <button @click="closeGradingModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Questions and Answers -->
            <div class="lg:col-span-2">
              <div class="max-h-96 overflow-y-auto space-y-4">
                <div v-for="(question, index) in exam.questions" :key="question.id"
                     class="border border-gray-200 rounded-lg p-4">
                  <div class="flex justify-between items-start mb-3">
                    <h4 class="font-medium">Question {{ index + 1 }} ({{ question.points }} points)</h4>
                    <span class="text-sm text-gray-500">{{ question.type }}</span>
                  </div>

                  <p class="text-gray-700 mb-3">{{ question.question }}</p>

                  <!-- Show correct answer for auto-gradable questions -->
                  <div v-if="question.type === 'multiple-choice' || question.type === 'true-false'"
                       class="mb-3 p-2 bg-green-50 rounded">
                    <p class="text-sm text-green-800">
                      <strong>Correct Answer:</strong> {{ getCorrectAnswerText(question) }}
                    </p>
                  </div>

                  <!-- Student's answer -->
                  <div class="mb-3 p-2 bg-blue-50 rounded">
                    <p class="text-sm text-blue-800">
                      <strong>Student's Answer:</strong> {{ getStudentAnswerText(question, selectedSubmission) }}
                    </p>
                  </div>

                  <!-- Manual grading for essays and short answers -->
                  <div v-if="question.type === 'essay' || question.type === 'short-answer'"
                       class="grid grid-cols-2 gap-2">
                    <div>
                      <label class="form-label text-xs">Points Awarded</label>
                      <input v-model.number="questionScores[question.id]"
                             type="number"
                             :max="question.points"
                             min="0"
                             class="form-input text-sm" />
                    </div>
                    <div class="flex items-end">
                      <span class="text-xs text-gray-500">/ {{ question.points }} points</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Grading Summary -->
            <div class="lg:col-span-1">
              <div class="card">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Grading Summary</h4>

                <div class="space-y-3 mb-6">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Total Points:</span>
                    <span class="font-medium">{{ exam.totalPoints }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Earned Points:</span>
                    <span class="font-medium">{{ totalEarnedPoints }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Percentage:</span>
                    <span class="font-medium">{{ Math.round((totalEarnedPoints / exam.totalPoints) * 100) }}%</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Grade:</span>
                    <span class="font-medium">{{ calculateGrade(totalEarnedPoints, exam.totalPoints) }}</span>
                  </div>
                </div>

                <div class="mb-6">
                  <label class="form-label">Feedback (Optional)</label>
                  <textarea v-model="gradingFeedback" rows="4" class="form-input text-sm"
                            placeholder="Provide feedback to the student..."></textarea>
                </div>

                <div class="space-y-3">
                  <button @click="saveGrade" :disabled="submissionStore.isLoading" class="btn-primary w-full">
                    {{ submissionStore.isLoading ? 'Saving...' : 'Save Grade' }}
                  </button>
                  <button @click="closeGradingModal" class="btn-secondary w-full">
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useExamStore } from '@/stores/exams'
import { useSubmissionStore } from '@/stores/submissions'
import { useUserStore } from '@/stores/users'
import type { Exam, ExamSubmission, Question } from '@/types'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const examStore = useExamStore()
const submissionStore = useSubmissionStore()
const userStore = useUserStore()

// State
const isLoading = ref(true)
const exam = ref<Exam | null>(null)
const searchQuery = ref('')
const statusFilter = ref('')
const showGradingModal = ref(false)
const selectedSubmission = ref<ExamSubmission | null>(null)
const questionScores = ref<Record<string, number>>({})
const gradingFeedback = ref('')

// Computed properties
const submissions = computed(() =>
  exam.value ? submissionStore.submissionsByExam(exam.value.id).filter(s => s.isCompleted) : []
)

const gradedSubmissions = computed(() =>
  submissions.value.filter(s => s.isGraded)
)

const pendingSubmissions = computed(() =>
  submissions.value.filter(s => !s.isGraded)
)

const averageScore = computed(() => {
  const graded = gradedSubmissions.value
  if (graded.length === 0) return 0
  const total = graded.reduce((sum, s) => sum + (s.percentage || 0), 0)
  return Math.round(total / graded.length)
})

const filteredSubmissions = computed(() => {
  let filtered = submissions.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(submission => {
      const student = userStore.getUserById(submission.studentId)
      return student?.name.toLowerCase().includes(query)
    })
  }

  // Status filter
  if (statusFilter.value) {
    switch (statusFilter.value) {
      case 'graded':
        filtered = filtered.filter(s => s.isGraded)
        break
      case 'pending':
        filtered = filtered.filter(s => !s.isGraded)
        break
      case 'submitted':
        filtered = filtered.filter(s => s.status === 'submitted')
        break
    }
  }

  return filtered
})

const totalEarnedPoints = computed(() => {
  if (!exam.value) return 0

  let total = 0
  for (const question of exam.value.questions) {
    if (question.type === 'multiple-choice' || question.type === 'true-false') {
      // Auto-graded questions
      const answer = selectedSubmission.value?.answers.find(a => a.questionId === question.id)
      if (answer && answer.answer === question.correctAnswer) {
        total += question.points
      }
    } else {
      // Manually graded questions
      total += questionScores.value[question.id] || 0
    }
  }
  return total
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/login')
}

const loadData = async () => {
  isLoading.value = true
  try {
    const examId = route.params.examId as string

    await Promise.all([
      examStore.loadExams(),
      submissionStore.loadSubmissions(),
      userStore.loadUsers()
    ])

    exam.value = examStore.getExamById(examId) || null
  } catch (error) {
    console.error('Error loading data:', error)
  } finally {
    isLoading.value = false
  }
}

const getStudentName = (studentId: string): string => {
  const student = userStore.getUserById(studentId)
  return student?.name || 'Unknown Student'
}

const getStudentInitials = (studentId: string): string => {
  const student = userStore.getUserById(studentId)
  if (!student) return 'U'
  return student.firstName.charAt(0) + student.lastName.charAt(0)
}

const formatDate = (date?: Date): string => {
  if (!date) return 'Not submitted'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const formatStatus = (status: string): string => {
  switch (status) {
    case 'submitted':
      return 'Submitted'
    case 'graded':
      return 'Graded'
    case 'in-progress':
      return 'In Progress'
    default:
      return status
  }
}

const getStatusBadgeClass = (status: string): string => {
  switch (status) {
    case 'graded':
      return 'bg-green-100 text-green-800'
    case 'submitted':
      return 'bg-blue-100 text-blue-800'
    case 'in-progress':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const viewSubmission = (submission: ExamSubmission) => {
  selectedSubmission.value = submission
  initializeQuestionScores()
  showGradingModal.value = true
}

const gradeSubmission = (submission: ExamSubmission) => {
  viewSubmission(submission)
}

const editGrade = (submission: ExamSubmission) => {
  viewSubmission(submission)
  gradingFeedback.value = submission.feedback || ''
}

const initializeQuestionScores = () => {
  if (!exam.value || !selectedSubmission.value) return

  questionScores.value = {}
  for (const question of exam.value.questions) {
    if (question.type === 'essay' || question.type === 'short-answer') {
      // Initialize with existing score if available
      questionScores.value[question.id] = 0
    }
  }
}

const getCorrectAnswerText = (question: Question): string => {
  if (question.type === 'multiple-choice' && question.options) {
    return question.options[question.correctAnswer as number] || 'Invalid'
  } else if (question.type === 'true-false') {
    return question.correctAnswer === 0 ? 'True' : 'False'
  }
  return 'N/A'
}

const getStudentAnswerText = (question: Question, submission: ExamSubmission): string => {
  const answer = submission.answers.find(a => a.questionId === question.id)
  if (!answer) return 'Not answered'

  if (question.type === 'multiple-choice' && question.options) {
    return question.options[answer.answer as number] || 'Invalid'
  } else if (question.type === 'true-false') {
    return answer.answer === 0 ? 'True' : 'False'
  }
  return answer.answer.toString()
}

const calculateGrade = (earnedPoints: number, totalPoints: number): string => {
  const percentage = (earnedPoints / totalPoints) * 100
  if (percentage >= 90) return 'A'
  if (percentage >= 80) return 'B'
  if (percentage >= 70) return 'C'
  if (percentage >= 60) return 'D'
  return 'F'
}

const saveGrade = async () => {
  if (!selectedSubmission.value || !exam.value) return

  const result = await submissionStore.gradeSubmission(
    selectedSubmission.value.id,
    totalEarnedPoints.value,
    gradingFeedback.value,
    authStore.user?.id
  )

  if (result.success) {
    closeGradingModal()
    console.log('Grade saved successfully')
  } else {
    alert('Failed to save grade: ' + result.message)
  }
}

const autoGradeAll = async () => {
  if (!exam.value) return

  const ungraded = pendingSubmissions.value
  if (ungraded.length === 0) {
    alert('No submissions to grade')
    return
  }

  if (!confirm(`Auto-grade ${ungraded.length} submissions? This will only grade multiple choice and true/false questions.`)) {
    return
  }

  for (const submission of ungraded) {
    let score = 0
    for (const question of exam.value.questions) {
      if (question.type === 'multiple-choice' || question.type === 'true-false') {
        const answer = submission.answers.find(a => a.questionId === question.id)
        if (answer && answer.answer === question.correctAnswer) {
          score += question.points
        }
      }
    }

    await submissionStore.gradeSubmission(
      submission.id,
      score,
      'Auto-graded (multiple choice and true/false questions only)',
      authStore.user?.id
    )
  }

  console.log('Auto-grading completed')
}

const closeGradingModal = () => {
  showGradingModal.value = false
  selectedSubmission.value = null
  questionScores.value = {}
  gradingFeedback.value = ''
}

onMounted(async () => {
  await loadData()
})
</script>