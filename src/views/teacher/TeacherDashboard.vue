<template>
  <div class="min-h-screen bg-gray-50">
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">Teacher Dashboard</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">Welcome, {{ authStore.user?.name }}</span>
            <button @click="logout" class="btn-secondary">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Stats Cards -->
          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">My Exams</h3>
            <p class="text-3xl font-bold text-blue-600">{{ stats.myExams }}</p>
          </div>

          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">Total Submissions</h3>
            <p class="text-3xl font-bold text-green-600">{{ stats.totalSubmissions }}</p>
          </div>

          <div class="card">
            <h3 class="text-lg font-medium text-gray-900">Pending Grading</h3>
            <p class="text-3xl font-bold text-orange-600">{{ stats.pendingGrading }}</p>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <router-link to="/teacher/exams/create" class="card hover:shadow-lg transition-shadow cursor-pointer">
              <h3 class="text-lg font-medium text-gray-900">Create New Exam</h3>
              <p class="text-gray-600">Design and publish a new exam for students</p>
            </router-link>

            <router-link to="/teacher/exams" class="card hover:shadow-lg transition-shadow cursor-pointer">
              <h3 class="text-lg font-medium text-gray-900">Manage Exams</h3>
              <p class="text-gray-600">View and edit your existing exams</p>
            </router-link>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const stats = ref({
  myExams: 0,
  totalSubmissions: 0,
  pendingGrading: 0
})

const logout = () => {
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  // Mock data
  stats.value = {
    myExams: 8,
    totalSubmissions: 45,
    pendingGrading: 12
  }
})
</script>